{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-20T06:09:27.067Z", "updatedAt": "2025-08-20T06:09:27.075Z", "resourceCount": 7}, "resources": [{"id": "english-tutor", "source": "project", "protocol": "role", "name": "English Tutor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/english-tutor/english-tutor.role.md", "metadata": {"createdAt": "2025-08-20T06:09:27.072Z", "updatedAt": "2025-08-20T06:09:27.072Z", "scannedAt": "2025-08-20T06:09:27.072Z", "path": "role/english-tutor/english-tutor.role.md"}}, {"id": "english-tutoring-workflow", "source": "project", "protocol": "execution", "name": "English Tutoring Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/english-tutor/execution/english-tutoring-workflow.execution.md", "metadata": {"createdAt": "2025-08-20T06:09:27.073Z", "updatedAt": "2025-08-20T06:09:27.073Z", "scannedAt": "2025-08-20T06:09:27.073Z", "path": "role/english-tutor/execution/english-tutoring-workflow.execution.md"}}, {"id": "adaptive-teaching", "source": "project", "protocol": "thought", "name": "Adaptive Teaching 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/english-tutor/thought/adaptive-teaching.thought.md", "metadata": {"createdAt": "2025-08-20T06:09:27.073Z", "updatedAt": "2025-08-20T06:09:27.073Z", "scannedAt": "2025-08-20T06:09:27.073Z", "path": "role/english-tutor/thought/adaptive-teaching.thought.md"}}, {"id": "stm32-teaching-process", "source": "project", "protocol": "execution", "name": "Stm32 Teaching Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stm32-teacher/execution/stm32-teaching-process.execution.md", "metadata": {"createdAt": "2025-08-20T06:09:27.074Z", "updatedAt": "2025-08-20T06:09:27.074Z", "scannedAt": "2025-08-20T06:09:27.074Z", "path": "role/stm32-teacher/execution/stm32-teaching-process.execution.md"}}, {"id": "stm32-teaching-knowledge", "source": "project", "protocol": "knowledge", "name": "Stm32 Teaching Knowledge 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/stm32-teacher/knowledge/stm32-teaching-knowledge.knowledge.md", "metadata": {"createdAt": "2025-08-20T06:09:27.074Z", "updatedAt": "2025-08-20T06:09:27.074Z", "scannedAt": "2025-08-20T06:09:27.074Z", "path": "role/stm32-teacher/knowledge/stm32-teaching-knowledge.knowledge.md"}}, {"id": "stm32-teacher", "source": "project", "protocol": "role", "name": "Stm32 Teacher 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stm32-teacher/stm32-teacher.role.md", "metadata": {"createdAt": "2025-08-20T06:09:27.074Z", "updatedAt": "2025-08-20T06:09:27.074Z", "scannedAt": "2025-08-20T06:09:27.074Z", "path": "role/stm32-teacher/stm32-teacher.role.md"}}, {"id": "stm32-teaching", "source": "project", "protocol": "thought", "name": "Stm32 Teaching 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stm32-teacher/thought/stm32-teaching.thought.md", "metadata": {"createdAt": "2025-08-20T06:09:27.075Z", "updatedAt": "2025-08-20T06:09:27.075Z", "scannedAt": "2025-08-20T06:09:27.075Z", "path": "role/stm32-teacher/thought/stm32-teaching.thought.md"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 2, "execution": 2, "thought": 2, "knowledge": 1}, "bySource": {"project": 7}}}