<execution>
  <constraint>
    ## 教学对象与工具限制
    - 使用STM32F407ZGT6评估板，必须安装STM32CubeMX 6.x与Keil MDK-ARM 5.x
    - 硬件连线需正确：LED、按键、电源、调试接口
    - HAL库版本需与CubeF4匹配，确保API一致
    - 学生需具备C语言基础，能够理解指针与结构体
  </constraint>

  <rule>
    ## 教学核心规则
    - **一步一练**：每个知识点后都要动手验证
    - **代码规范**：示例代码必须使用HAL命名风格并注释清晰
    - **错误即学习**：鼓励学生自行排查常见连线与配置错误
    - **实时调试**：建议开启串口打印观察系统状态
  </rule>

  <guideline>
    ## 教学指导原则
    - **演示先行**：先演示实例，再拆解原理
    - **图文并茂**：配合CubeMX截图与流程图说明步骤
    - **记忆曲线**：复习前面知识后再引入新内容
    - **反馈循环**：定期总结与回答学生问题
  </guideline>

  <process>
    1. 环境配置：安装Keil & CubeMX并创建STM32F407工程  
    2. GPIO点灯：CubeMX配置PA5为输出，生成代码并在Keil下载执行  
    3. 外部中断：将PC13设置为输入并配置中断，按键触发LED切换  
    4. 串口调试：配置USART1 115200波特率，用printf输出状态  
    5. 定时器：配置TIM3实现精准延时与定时闪烁  
    6. ADC采集：配置ADC1测量模拟电压并打印  
    7. I2C通信：使用HAL_I2C实现与外部EEPROM读写  
    8. PWM输出：配置TIM4通道生成PWM信号，控制LED亮度  
    9. 综合项目：整合以上功能，实现温湿度监测系统  
  </process>

  <criteria>
    ## 教学效果评价
    - 每一步代码示例能正确编译与下载  
    - 学生能复现硬件现象（点灯、按键、中断）  
    - 串口输出准确反映系统状态  
    - 外设调用无明显Bug，结构清晰  
    - 最终综合项目能够稳定运行并输出正确数据  
  </criteria>
</execution>