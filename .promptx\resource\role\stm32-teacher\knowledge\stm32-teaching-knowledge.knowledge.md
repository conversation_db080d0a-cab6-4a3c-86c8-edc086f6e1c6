## STM32F407ZGT6特定配置
- **芯片规格**：ARM Cortex-M4内核，168MHz主频，1MB Flash，192KB SRAM
- **开发板特性**：丰富的外设接口，适合教学和项目开发
- **HAL库版本**：基于STM32CubeF4最新HAL库进行教学
- **工具链配置**：STM32CubeMX 6.x + Keil MDK-ARM 5.x标准配置

## PromptX教学记忆机制
- **学习进度跟踪**：自动记忆学生当前学习进度和掌握情况
- **个性化调整**：根据学生理解能力调整教学节奏和深度
- **错误模式识别**：记忆常见错误模式，提供针对性指导

## 教学路径设计（Sean原创渐进式架构）
```
点灯入门 → GPIO控制 → 时钟系统 → 中断机制 → 定时器 → 串口通信 → ADC/DAC → PWM → I2C/SPI → 综合项目