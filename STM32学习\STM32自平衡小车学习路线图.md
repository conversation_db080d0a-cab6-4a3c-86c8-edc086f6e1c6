# STM32两轮自平衡小车项目学习路线图

你好！这是一个为你量身定制的学习计划，旨在帮助你从零开始，一步步完成基于STM32、HAL库，并包含自制PCB和3D打印车体的两轮自平衡小车项目。

---

## 阶段一：基础知识储备 (预计2-3周)

这个阶段的目标是掌握STM32开发和项目所需的基础电子知识。

### 1. **STM32入门与HAL库**
- **学习目标**：理解STM32单片机的基本架构，并熟练使用CubeMX图形化配置工具和HAL库进行开发。
- **关键知识点**：
    - **开发环境搭建**：安装 Keil MDK/STM32CubeIDE、STM32CubeMX。
    - **C语言复习**：重点复习指针、结构体、位运算，这是嵌入式开发的基础。
    - **GPIO（通用输入输出）**：学习使用HAL库控制LED灯闪烁、读取按键状态。这是最基本的硬件操作。
    - **UART（通用异步收发器）**：学习使用UART实现单片机与电脑的通信，常用`printf`重定向进行调试信息输出，这是最重要的调试手段。
    - **TIM（定时器）**：
        - **基本定时**：学习使用定时器中断，实现精准延时。
        - **PWM（脉冲宽度调制）**：学习生成PWM波形，用于控制舵机和直流电机的转速。
        - **输入捕获**：学习使用定时器的输入捕获功能，用于读取编码器数据，获取电机转速。

### 2. **核心传感器与模块**
- **学习目标**：掌握小车核心模块的驱动和数据读取。
- **关键知识点**：
    - **I2C/SPI通信协议**：理解这两种常用的串行通信协议，用于单片机和传感器（如MPU6050）之间的数据交换。
    - **MPU6050（六轴姿态传感器）**：
        - 学习读取陀螺仪（Gyro）和加速度计（Accelerometer）的原始数据。
        - 理解数据融合算法（如互补滤波、卡尔曼滤波）的基本思想，将原始数据融合成稳定、精确的小车姿态角度。可以直接寻找移植现成的DMP库来简化开发。
    - **带编码器的直流电机**：了解其工作原理，编码器用于反馈电机转速，是实现速度闭环控制的关键。
    - **电机驱动模块（如TB6612FNG）**：学习如何使用驱动模块控制电机的正反转和转速。

### 3. **基础电子与PCB设计**
- **学习目标**：能够看懂电路原理图，并使用EDA工具设计简单的PCB。
- **关键知识点**：
    - **常用元器件**：认识电阻、电容、电感、二极管、三极管、MOS管、LDO稳压芯片等。
    - **EDA软件入门**：学习使用 KiCad 或 EasyEDA 等免费PCB设计软件。
    - **实践**：绘制一个最小系统板的原理图（STM32芯片、晶振、复位电路、电源电路），并尝试将其布局布线，生成Gerber文件。

---

## 阶段二：核心算法与控制 (预计1-2周)

这个阶段专注于平衡小车的“灵魂”——PID控制算法。

### 1. **PID控制算法**
- **学习目标**：深刻理解PID（比例-积分-微分）控制的原理，并能用代码实现。
- **关键知识点**：
    - **理论学习**：了解P、I、D三个环节各自的作用，以及它们如何协同工作。
    - **代码实现**：学习增量式/位置式PID代码的编写结构。
    - **双环PID控制**：
        - **平衡环（角度环）**：直立控制。输入是小车倾角，输出是期望的电机转速。这是核心。
        - **速度环**：速度控制。输入是期望速度与实际速度的差值，输出是最终作用到电机PWM的占空比。
    - **参数整定**：学习如何通过实际测试，调整P、I、D三个参数，使小车达到最佳的稳定效果（这是一个需要耐心的过程）。

---

## 阶段三：硬件与结构设计 (预计1-2周)

将理论付诸实践，设计出小车的“骨架”和“血液”。

### 1. **整车电路设计 (PCB)**
- **学习目标**：设计出平衡小车的完整主控板PCB。
- **关键知识点**：
    - **原理图设计**：整合STM32最小系统、电源模块（电池充放电、多级稳压）、MPU6050、电机驱动、接口（SWD下载、UART）等所有模块。
    - **PCB布局布线**：
        - **布局**：合理放置元器件，考虑信号流向和物理尺寸。
        - **布线**：电源线要粗，信号线避免交叉干扰，晶振等敏感电路要特殊处理。
    - **生产与焊接**：将Gerber文件发给PCB厂商打样，并自行完成元器件的焊接。

### 2. **车体结构设计 (3D打印)**
- **学习目标**：使用3D建模软件设计小车底盘并打印。
- **关键知识点**：
    - **3D建模软件入门**：学习使用 Fusion 360, SolidWorks, 或 FreeCAD 等。
    - **结构设计**：设计能够稳固安装PCB、电机、电池、轮子的车架，注意重心要低。
    - **3D打印**：了解3D打印的基本流程和注意事项。

---

## 阶段四：系统联调与优化 (预计1-2周)

这是项目收尾阶段，将所有部分组装起来，让小车真正“跑起来”。

### 1. **固件编写与整合**
- **学习目标**：编写最终的控制程序，将所有模块的功能整合在一起。
- **关键知识点**：
    - **代码框架**：建立一个清晰的主循环，定时读取传感器数据，执行PID运算，并更新电机状态。
    - **调试**：使用串口打印关键变量（如角度、电机PWM值、PID各分量输出），观察系统运行状态，进行参数微调。

### 2. **问题排查与优化**
- **学习目标**：解决联调中遇到的各种软硬件问题。
- **常见问题**：
    - **硬件问题**：虚焊、短路、元器件损坏。
    - **软件问题**：PID参数不佳、数据滤波效果不好、程序逻辑错误。
    - **结构问题**：重心过高、车体晃动。

---

**祝你项目顺利，享受从0到1创造的乐趣！**
