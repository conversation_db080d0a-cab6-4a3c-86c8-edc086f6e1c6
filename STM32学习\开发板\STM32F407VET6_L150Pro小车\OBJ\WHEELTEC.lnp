--cpu=Cortex-M4.fp
"..\obj\main.o"
"..\obj\stm32f4xx_it.o"
"..\obj\system_stm32f4xx.o"
"..\obj\adc.o"
"..\obj\can.o"
"..\obj\encoder.o"
"..\obj\key.o"
"..\obj\led.o"
"..\obj\motor.o"
"..\obj\oled.o"
"..\obj\pstwo.o"
"..\obj\timer.o"
"..\obj\usartx.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\startup_stm32f40_41xxx.o"
"..\obj\misc.o"
"..\obj\stm32f4xx_gpio.o"
"..\obj\stm32f4xx_rcc.o"
"..\obj\stm32f4xx_syscfg.o"
"..\obj\stm32f4xx_usart.o"
"..\obj\stm32f4xx_adc.o"
"..\obj\stm32f4xx_can.o"
"..\obj\stm32f4xx_tim.o"
"..\obj\stm32f4xx_pwr.o"
"..\obj\balance.o"
"..\obj\datascope_dp.o"
"..\obj\filter.o"
"..\obj\robot_select_init.o"
"..\obj\show.o"
"..\obj\system.o"
"..\obj\i2c.o"
"..\obj\mpu6050.o"
--strict --scatter "..\OBJ\WHEELTEC.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\OBJ\WHEELTEC.map" -o ..\OBJ\WHEELTEC.axf