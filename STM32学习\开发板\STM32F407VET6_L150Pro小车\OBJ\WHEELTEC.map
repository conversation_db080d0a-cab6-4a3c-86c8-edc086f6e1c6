Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.main) refers to system.o(i.systemInit) for systemInit
    main.o(i.main) refers to show.o(i.show_task) for show_task
    main.o(i.main) refers to pstwo.o(i.PS2_Read) for PS2_Read
    main.o(i.main) refers to usartx.o(i.data_task) for data_task
    main.o(i.main) refers to usartx.o(.data) for data_sent_flag
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_POWER_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Get_Adc) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    adc.o(i.Get_Adc) refers to stm32f4xx_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc) refers to stm32f4xx_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_Adc2) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc2) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    adc.o(i.Get_Adc2) refers to stm32f4xx_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc2) refers to stm32f4xx_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_adc_Average) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    adc.o(i.Get_battery_volt) refers to adc.o(i.Get_Adc2) for Get_Adc2
    adc.o(i.Get_battery_volt) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    adc.o(i.Get_battery_volt) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(i.Get_battery_volt) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    can.o(i.CAN1_Mode_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_can.o(i.CAN_ITConfig) for CAN_ITConfig
    can.o(i.CAN1_RX0_IRQHandler) refers to can.o(i.CAN1_Rx_Msg) for CAN1_Rx_Msg
    can.o(i.CAN1_RX0_IRQHandler) refers to usartx.o(i.Vz_to_Akm_Angle) for Vz_to_Akm_Angle
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Usart1_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Usart5_ON_Flag
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Move_X
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Move_Y
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Car_Mode
    can.o(i.CAN1_RX0_IRQHandler) refers to system.o(.data) for Move_Z
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Msg_Pend) for CAN1_Msg_Pend
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Rx_Msg) for CAN1_Rx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    can.o(i.CAN1_Send_MsgTEST) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_MsgTEST) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    can.o(i.CAN1_Send_Num) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_Num) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init_TIM2) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init_TIM3) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init_TIM4) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init_TIM5) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    key.o(i.KEY_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Long_Press) refers to key.o(.data) for .data
    key.o(i.click) refers to key.o(.data) for .data
    key.o(i.click_N_Double) refers to key.o(.data) for .data
    key.o(i.click_N_Double_MPU6050) refers to key.o(.data) for .data
    led.o(i.Buzzer_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.Buzzer_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.Led_Flash) refers to led.o(.data) for .data
    motor.o(i.Enable_Pin) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    motor.o(i.Enable_Pin) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    motor.o(i.TIM10_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    motor.o(i.TIM11_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    motor.o(i.TIM1_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    motor.o(i.TIM9_PWM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f4xx_pwr.o(i.PWR_BackupAccessCmd) for PWR_BackupAccessCmd
    oled.o(i.OLED_Init) refers to stm32f4xx_rcc.o(i.RCC_LSEConfig) for RCC_LSEConfig
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNumber) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNumber) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    pstwo.o(i.PS2_AnologData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_ClearData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Cmd) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Cmd) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(i.PS2_ClearData) for PS2_ClearData
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(i.PS2_ReadData) for PS2_ReadData
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_DataKey) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_EnterConfing) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_EnterConfing) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_ExitConfing) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ExitConfing) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    pstwo.o(i.PS2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    pstwo.o(i.PS2_Read) refers to pstwo.o(i.PS2_DataKey) for PS2_DataKey
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_KEY
    pstwo.o(i.PS2_Read) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_LX
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_LY
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_RX
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_RY
    pstwo.o(i.PS2_Read) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_Read) refers to system.o(.data) for PS2_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Remote_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for APP_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for CAN_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Usart1_ON_Flag
    pstwo.o(i.PS2_Read) refers to system.o(.data) for Usart5_ON_Flag
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_ReadData) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_ReadData) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Receive) refers to pstwo.o(i.PS2_DataKey) for PS2_DataKey
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_ON_Flag
    pstwo.o(i.PS2_Receive) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_LX
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_LY
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_RX
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_RY
    pstwo.o(i.PS2_Receive) refers to system.o(.data) for PS2_KEY
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(.data) for .data
    pstwo.o(i.PS2_RedLight) refers to pstwo.o(.bss) for .bss
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_ShortPoll) for PS2_ShortPoll
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_EnterConfing) for PS2_EnterConfing
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_TurnOnAnalogMode) for PS2_TurnOnAnalogMode
    pstwo.o(i.PS2_SetInit) refers to pstwo.o(i.PS2_ExitConfing) for PS2_ExitConfing
    pstwo.o(i.PS2_ShortPoll) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_ShortPoll) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_TurnOnAnalogMode) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_TurnOnAnalogMode) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Vibration) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_Vibration) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    pstwo.o(i.PS2_VibrationMode) refers to delay.o(i.delay_us) for delay_us
    pstwo.o(i.PS2_VibrationMode) refers to pstwo.o(i.PS2_Cmd) for PS2_Cmd
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer.o(i.TIM12_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM7_Int_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM7_Int_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM7_Int_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM7_Int_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM7_Int_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetCapture1) for TIM_GetCapture1
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_OC1PolarityConfig) for TIM_OC1PolarityConfig
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetCapture2) for TIM_GetCapture2
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_OC2PolarityConfig) for TIM_OC2PolarityConfig
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetCapture3) for TIM_GetCapture3
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_OC3PolarityConfig) for TIM_OC3PolarityConfig
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetCapture4) for TIM_GetCapture4
    timer.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_OC4PolarityConfig) for TIM_OC4PolarityConfig
    timer.o(i.TIM8_CC_IRQHandler) refers to timer.o(.data) for .data
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for Usart1_ON_Flag
    timer.o(i.TIM8_CC_IRQHandler) refers to system.o(.data) for Usart5_ON_Flag
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    timer.o(i.TIM8_Cap_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer.o(i.TIM8_Cap_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer.o(i.TIM8_SERVO_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    usartx.o(i.CAN_SEND) refers to can.o(i.CAN1_Send_Num) for CAN1_Send_Num
    usartx.o(i.CAN_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.Check_Sum) refers to usartx.o(.bss) for .bss
    usartx.o(i.UART5_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usartx.o(i.UART5_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usartx.o(i.UART5_IRQHandler) refers to usartx.o(i.Check_Sum) for Check_Sum
    usartx.o(i.UART5_IRQHandler) refers to usartx.o(i.XYZ_Target_Speed_transition) for XYZ_Target_Speed_transition
    usartx.o(i.UART5_IRQHandler) refers to usartx.o(i.Vz_to_Akm_Angle) for Vz_to_Akm_Angle
    usartx.o(i.UART5_IRQHandler) refers to balance.o(.data) for Time_count
    usartx.o(i.UART5_IRQHandler) refers to usartx.o(.bss) for .bss
    usartx.o(i.UART5_IRQHandler) refers to usartx.o(.data) for .data
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Usart5_ON_Flag
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Move_X
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Move_Y
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Car_Mode
    usartx.o(i.UART5_IRQHandler) refers to system.o(.data) for Move_Z
    usartx.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usartx.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usartx.o(i.USART1_IRQHandler) refers to usartx.o(i.XYZ_Target_Speed_transition) for XYZ_Target_Speed_transition
    usartx.o(i.USART1_IRQHandler) refers to usartx.o(i.Vz_to_Akm_Angle) for Vz_to_Akm_Angle
    usartx.o(i.USART1_IRQHandler) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART1_IRQHandler) refers to usartx.o(.data) for .data
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Usart1_ON_Flag
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Move_X
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Move_Y
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Car_Mode
    usartx.o(i.USART1_IRQHandler) refers to system.o(.data) for Move_Z
    usartx.o(i.USART1_SEND) refers to usartx.o(i.usart1_send) for usart1_send
    usartx.o(i.USART1_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART2_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usartx.o(i.USART2_IRQHandler) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    usartx.o(i.USART2_IRQHandler) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    usartx.o(i.USART2_IRQHandler) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.USART2_IRQHandler) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.USART2_IRQHandler) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    usartx.o(i.USART2_IRQHandler) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.USART2_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usartx.o(i.USART2_IRQHandler) refers to usartx.o(.data) for .data
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Usart1_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Usart5_ON_Flag
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Flag_Direction
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Flag_Left
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Flag_Right
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Turn_Flag
    usartx.o(i.USART2_IRQHandler) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for RC_Velocity
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for PID_Send
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Velocity_KP
    usartx.o(i.USART2_IRQHandler) refers to system.o(.data) for Velocity_KI
    usartx.o(i.USART3_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usartx.o(i.USART3_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usartx.o(i.USART3_IRQHandler) refers to usartx.o(i.Check_Sum) for Check_Sum
    usartx.o(i.USART3_IRQHandler) refers to usartx.o(i.XYZ_Target_Speed_transition) for XYZ_Target_Speed_transition
    usartx.o(i.USART3_IRQHandler) refers to usartx.o(i.Vz_to_Akm_Angle) for Vz_to_Akm_Angle
    usartx.o(i.USART3_IRQHandler) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART3_IRQHandler) refers to usartx.o(.data) for .data
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for CAN_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Usart1_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Usart5_ON_Flag
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Move_X
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Move_Y
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Car_Mode
    usartx.o(i.USART3_IRQHandler) refers to system.o(.data) for Move_Z
    usartx.o(i.USART3_SEND) refers to usartx.o(i.usart3_send) for usart3_send
    usartx.o(i.USART3_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.USART5_SEND) refers to usartx.o(i.usart5_send) for usart5_send
    usartx.o(i.USART5_SEND) refers to usartx.o(.bss) for .bss
    usartx.o(i.Vz_to_Akm_Angle) refers to balance.o(i.float_abs) for float_abs
    usartx.o(i.Vz_to_Akm_Angle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.Vz_to_Akm_Angle) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    usartx.o(i.Vz_to_Akm_Angle) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.Vz_to_Akm_Angle) refers to system.o(.data) for Wheel_spacing
    usartx.o(i.Vz_to_Akm_Angle) refers to system.o(.data) for Axle_spacing
    usartx.o(i.XYZ_Target_Speed_transition) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    usartx.o(i.XYZ_Target_Speed_transition) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.XYZ_Target_Speed_transition) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    usartx.o(i.XYZ_Target_Speed_transition) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    usartx.o(i.data_task) refers to usartx.o(i.data_transition) for data_transition
    usartx.o(i.data_task) refers to usartx.o(i.USART1_SEND) for USART1_SEND
    usartx.o(i.data_task) refers to usartx.o(i.USART3_SEND) for USART3_SEND
    usartx.o(i.data_task) refers to usartx.o(i.USART5_SEND) for USART5_SEND
    usartx.o(i.data_task) refers to usartx.o(i.CAN_SEND) for CAN_SEND
    usartx.o(i.data_task) refers to usartx.o(.data) for .data
    usartx.o(i.data_transition) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    usartx.o(i.data_transition) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    usartx.o(i.data_transition) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usartx.o(i.data_transition) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    usartx.o(i.data_transition) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    usartx.o(i.data_transition) refers to usartx.o(i.Check_Sum) for Check_Sum
    usartx.o(i.data_transition) refers to usartx.o(.bss) for .bss
    usartx.o(i.data_transition) refers to system.o(.data) for Car_Mode
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_A
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_B
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_C
    usartx.o(i.data_transition) refers to system.o(.bss) for MOTOR_D
    usartx.o(i.data_transition) refers to system.o(.data) for Axle_spacing
    usartx.o(i.data_transition) refers to system.o(.data) for Wheel_spacing
    usartx.o(i.data_transition) refers to system.o(.data) for Omni_turn_radiaus
    usartx.o(i.data_transition) refers to mpu6050.o(.data) for accel
    usartx.o(i.data_transition) refers to system.o(.data) for Flag_Stop
    usartx.o(i.data_transition) refers to adc.o(.data) for Voltage
    usartx.o(i.uart1_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usartx.o(i.uart1_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usartx.o(i.uart1_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usartx.o(i.uart1_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usartx.o(i.uart1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usartx.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usartx.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usartx.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usartx.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usartx.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usartx.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usartx.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usartx.o(i.uart2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usartx.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usartx.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usartx.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usartx.o(i.uart3_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usartx.o(i.uart3_init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usartx.o(i.uart3_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usartx.o(i.uart3_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usartx.o(i.uart3_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usartx.o(i.uart3_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usartx.o(i.uart3_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usartx.o(i.uart3_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usartx.o(i.uart5_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usartx.o(i.uart5_init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usartx.o(i.uart5_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usartx.o(i.uart5_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usartx.o(i.uart5_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usartx.o(i.uart5_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usartx.o(i.uart5_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usartx.o(i.uart5_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    delay.o(i.delay_xms) refers to delay.o(.data) for .data
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to can.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to encoder.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to encoder.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to encoder.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usartx.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usartx.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usartx.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to encoder.o(i.TIM8_BRK_TIM12_IRQHandler) for TIM8_BRK_TIM12_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(i.TIM8_UP_TIM13_IRQHandler) for TIM8_UP_TIM13_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to encoder.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usartx.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to balance.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for .data
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    balance.o(i.Drive_Motor) refers to balance.o(i.Smooth_control) for Smooth_control
    balance.o(i.Drive_Motor) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Drive_Motor) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    balance.o(i.Drive_Motor) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    balance.o(i.Drive_Motor) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    balance.o(i.Drive_Motor) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    balance.o(i.Drive_Motor) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    balance.o(i.Drive_Motor) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    balance.o(i.Drive_Motor) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    balance.o(i.Drive_Motor) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    balance.o(i.Drive_Motor) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    balance.o(i.Drive_Motor) refers to balance.o(i.target_limit_int) for target_limit_int
    balance.o(i.Drive_Motor) refers to system.o(.data) for Car_Mode
    balance.o(i.Drive_Motor) refers to system.o(.data) for Axle_spacing
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_D
    balance.o(i.Drive_Motor) refers to system.o(.data) for Wheel_spacing
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Drive_Motor) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Drive_Motor) refers to system.o(.bss) for smooth_control
    balance.o(i.Drive_Motor) refers to system.o(.data) for Omni_turn_radiaus
    balance.o(i.Drive_Motor) refers to system.o(.data) for Servo
    balance.o(i.Get_RC) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.Get_RC) refers to system.o(.data) for Car_Mode
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Left
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Right
    balance.o(i.Get_RC) refers to system.o(.data) for Flag_Direction
    balance.o(i.Get_RC) refers to system.o(.data) for Move_Y
    balance.o(i.Get_RC) refers to system.o(.data) for RC_Velocity
    balance.o(i.Get_RC) refers to system.o(.data) for Move_X
    balance.o(i.Get_RC) refers to system.o(.data) for Move_Z
    balance.o(i.Get_Velocity_Form_Encoder) refers to encoder.o(i.Read_Encoder) for Read_Encoder
    balance.o(i.Get_Velocity_Form_Encoder) refers to balance.o(.bss) for .bss
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Car_Mode
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Wheel_perimeter
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.data) for Encoder_precision
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Get_Velocity_Form_Encoder) refers to system.o(.bss) for MOTOR_D
    balance.o(i.Incremental_PI_A) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_A) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_A) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_B) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_B) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_B) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_C) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_C) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_C) refers to system.o(.data) for Velocity_KI
    balance.o(i.Incremental_PI_D) refers to balance.o(.data) for .data
    balance.o(i.Incremental_PI_D) refers to system.o(.data) for Velocity_KP
    balance.o(i.Incremental_PI_D) refers to system.o(.data) for Velocity_KI
    balance.o(i.Key) refers to key.o(i.click_N_Double_MPU6050) for click_N_Double_MPU6050
    balance.o(i.Key) refers to mpu6050.o(.data) for Deviation_gyro
    balance.o(i.Key) refers to mpu6050.o(.data) for Original_gyro
    balance.o(i.Limit_Pwm) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_A
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_B
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_C
    balance.o(i.Limit_Pwm) refers to system.o(.bss) for MOTOR_D
    balance.o(i.PS2_control) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_LX
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_LY
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_RX
    balance.o(i.PS2_control) refers to system.o(.data) for PS2_KEY
    balance.o(i.PS2_control) refers to system.o(.data) for RC_Velocity
    balance.o(i.PS2_control) refers to system.o(.data) for Move_X
    balance.o(i.PS2_control) refers to system.o(.data) for Move_Y
    balance.o(i.PS2_control) refers to system.o(.data) for Move_Z
    balance.o(i.PS2_control) refers to system.o(.data) for Car_Mode
    balance.o(i.Remote_Control) refers to balance.o(i.target_limit_int) for target_limit_int
    balance.o(i.Remote_Control) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.Remote_Control) refers to timer.o(.data) for Remoter_Ch1
    balance.o(i.Remote_Control) refers to system.o(.data) for RC_Velocity
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_X
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_Y
    balance.o(i.Remote_Control) refers to system.o(.data) for Move_Z
    balance.o(i.Remote_Control) refers to system.o(.data) for Car_Mode
    balance.o(i.Remote_Control) refers to balance.o(.data) for .data
    balance.o(i.Smooth_control) refers to balance.o(i.float_abs) for float_abs
    balance.o(i.Smooth_control) refers to balance.o(i.target_limit_float) for target_limit_float
    balance.o(i.Smooth_control) refers to system.o(.bss) for smooth_control
    balance.o(i.TIM7_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    balance.o(i.TIM7_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Get_Velocity_Form_Encoder) for Get_Velocity_Form_Encoder
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Get_RC) for Get_RC
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Remote_Control) for Remote_Control
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.PS2_control) for PS2_control
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Drive_Motor) for Drive_Motor
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Key) for Key
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Turn_Off) for Turn_Off
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Incremental_PI_A) for Incremental_PI_A
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Incremental_PI_B) for Incremental_PI_B
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Incremental_PI_C) for Incremental_PI_C
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Incremental_PI_D) for Incremental_PI_D
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Limit_Pwm) for Limit_Pwm
    balance.o(i.TIM7_IRQHandler) refers to balance.o(i.Set_Pwm) for Set_Pwm
    balance.o(i.TIM7_IRQHandler) refers to mpu6050.o(i.MPU6050_task) for MPU6050_task
    balance.o(i.TIM7_IRQHandler) refers to led.o(i.Led_Flash) for Led_Flash
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Check
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for APP_ON_Flag
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Remote_ON_Flag
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for PS2_ON_Flag
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Move_Z
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Move_Y
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Move_X
    balance.o(i.TIM7_IRQHandler) refers to adc.o(.data) for Voltage
    balance.o(i.TIM7_IRQHandler) refers to system.o(.bss) for MOTOR_A
    balance.o(i.TIM7_IRQHandler) refers to system.o(.bss) for MOTOR_B
    balance.o(i.TIM7_IRQHandler) refers to system.o(.bss) for MOTOR_C
    balance.o(i.TIM7_IRQHandler) refers to system.o(.bss) for MOTOR_D
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Car_Mode
    balance.o(i.TIM7_IRQHandler) refers to balance.o(.data) for .data
    balance.o(i.TIM7_IRQHandler) refers to usartx.o(.data) for data_sent_flag
    balance.o(i.TIM7_IRQHandler) refers to system.o(.data) for Servo
    balance.o(i.Turn_Off) refers to system.o(.data) for Flag_Stop
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_A
    balance.o(i.robot_mode_check) refers to balance.o(.data) for .data
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_B
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_C
    balance.o(i.robot_mode_check) refers to system.o(.bss) for MOTOR_D
    balance.o(i.robot_mode_check) refers to system.o(.data) for Flag_Stop
    datascope_dp.o(i.DataScope_Data_Generate) refers to datascope_dp.o(.bss) for .bss
    datascope_dp.o(i.DataScope_Get_Channel_Data) refers to datascope_dp.o(i.Float2Byte) for Float2Byte
    datascope_dp.o(i.DataScope_Get_Channel_Data) refers to datascope_dp.o(.bss) for .bss
    filter.o(i.Kalman_Filter) refers to filter.o(.data) for .data
    filter.o(i.Kalman_Filter) refers to filter.o(.bss) for .bss
    filter.o(i.Yijielvbo) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    filter.o(i.Yijielvbo) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    filter.o(i.Yijielvbo) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    filter.o(i.Yijielvbo) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    filter.o(i.Yijielvbo) refers to filter.o(.data) for .data
    robot_select_init.o(i.Robot_Init) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    robot_select_init.o(i.Robot_Init) refers to robot_select_init.o(.bss) for .bss
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Encoder_precision
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Wheel_perimeter
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Wheel_spacing
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Axle_spacing
    robot_select_init.o(i.Robot_Init) refers to system.o(.data) for Omni_turn_radiaus
    robot_select_init.o(i.Robot_Select) refers to adc.o(i.Get_adc_Average) for Get_adc_Average
    robot_select_init.o(i.Robot_Select) refers to robot_select_init.o(i.Robot_Init) for Robot_Init
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for Divisor_Mode
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for Car_Mode
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for CheckPhrase1
    robot_select_init.o(i.Robot_Select) refers to system.o(.data) for CheckPhrase2
    show.o(i.APP_Show) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    show.o(i.APP_Show) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    show.o(i.APP_Show) refers to _printf_dec.o(.text) for _printf_int_dec
    show.o(i.APP_Show) refers to noretval__2printf.o(.text) for __2printf
    show.o(i.APP_Show) refers to adc.o(.data) for Voltage
    show.o(i.APP_Show) refers to system.o(.bss) for MOTOR_A
    show.o(i.APP_Show) refers to system.o(.bss) for MOTOR_B
    show.o(i.APP_Show) refers to show.o(.data) for .data
    show.o(i.APP_Show) refers to system.o(.data) for PID_Send
    show.o(i.APP_Show) refers to mpu6050.o(.data) for gyro
    show.o(i.APP_Show) refers to system.o(.data) for Velocity_KI
    show.o(i.APP_Show) refers to system.o(.data) for Velocity_KP
    show.o(i.APP_Show) refers to system.o(.data) for RC_Velocity
    show.o(i.oled_show) refers to adc.o(i.Get_adc_Average) for Get_adc_Average
    show.o(i.oled_show) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    show.o(i.oled_show) refers to oled.o(i.OLED_ShowNumber) for OLED_ShowNumber
    show.o(i.oled_show) refers to system.o(.data) for Divisor_Mode
    show.o(i.oled_show) refers to adc.o(.data) for Voltage
    show.o(i.oled_show) refers to show.o(.data) for .data
    show.o(i.oled_show) refers to system.o(.data) for Check
    show.o(i.oled_show) refers to system.o(.data) for Car_Mode
    show.o(i.oled_show) refers to mpu6050.o(.data) for gyro
    show.o(i.oled_show) refers to mpu6050.o(.data) for Deviation_gyro
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_A
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_B
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_C
    show.o(i.oled_show) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    show.o(i.oled_show) refers to system.o(.bss) for MOTOR_D
    show.o(i.oled_show) refers to usartx.o(.bss) for Send_Data
    show.o(i.oled_show) refers to system.o(.data) for Servo
    show.o(i.oled_show) refers to system.o(.data) for PS2_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for APP_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Remote_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for CAN_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Usart1_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Usart5_ON_Flag
    show.o(i.oled_show) refers to system.o(.data) for Flag_Stop
    show.o(i.show_task) refers to adc.o(i.Get_battery_volt) for Get_battery_volt
    show.o(i.show_task) refers to show.o(i.APP_Show) for APP_Show
    show.o(i.show_task) refers to show.o(i.oled_show) for oled_show
    show.o(i.show_task) refers to adc.o(.data) for Voltage_All
    show.o(i.show_task) refers to adc.o(.data) for Voltage
    system.o(i.systemInit) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    system.o(i.systemInit) refers to delay.o(i.delay_init) for delay_init
    system.o(i.systemInit) refers to led.o(i.LED_Init) for LED_Init
    system.o(i.systemInit) refers to led.o(i.Buzzer_Init) for Buzzer_Init
    system.o(i.systemInit) refers to motor.o(i.Enable_Pin) for Enable_Pin
    system.o(i.systemInit) refers to oled.o(i.OLED_Init) for OLED_Init
    system.o(i.systemInit) refers to key.o(i.KEY_Init) for KEY_Init
    system.o(i.systemInit) refers to usartx.o(i.uart1_init) for uart1_init
    system.o(i.systemInit) refers to usartx.o(i.uart2_init) for uart2_init
    system.o(i.systemInit) refers to usartx.o(i.uart3_init) for uart3_init
    system.o(i.systemInit) refers to usartx.o(i.uart5_init) for uart5_init
    system.o(i.systemInit) refers to adc.o(i.Adc_Init) for Adc_Init
    system.o(i.systemInit) refers to adc.o(i.Adc_POWER_Init) for Adc_POWER_Init
    system.o(i.systemInit) refers to can.o(i.CAN1_Mode_Init) for CAN1_Mode_Init
    system.o(i.systemInit) refers to robot_select_init.o(i.Robot_Select) for Robot_Select
    system.o(i.systemInit) refers to encoder.o(i.Encoder_Init_TIM2) for Encoder_Init_TIM2
    system.o(i.systemInit) refers to encoder.o(i.Encoder_Init_TIM3) for Encoder_Init_TIM3
    system.o(i.systemInit) refers to encoder.o(i.Encoder_Init_TIM4) for Encoder_Init_TIM4
    system.o(i.systemInit) refers to encoder.o(i.Encoder_Init_TIM5) for Encoder_Init_TIM5
    system.o(i.systemInit) refers to timer.o(i.TIM12_SERVO_Init) for TIM12_SERVO_Init
    system.o(i.systemInit) refers to timer.o(i.TIM8_Cap_Init) for TIM8_Cap_Init
    system.o(i.systemInit) refers to motor.o(i.TIM1_PWM_Init) for TIM1_PWM_Init
    system.o(i.systemInit) refers to motor.o(i.TIM9_PWM_Init) for TIM9_PWM_Init
    system.o(i.systemInit) refers to motor.o(i.TIM10_PWM_Init) for TIM10_PWM_Init
    system.o(i.systemInit) refers to motor.o(i.TIM11_PWM_Init) for TIM11_PWM_Init
    system.o(i.systemInit) refers to i2c.o(i.I2C_GPIOInit) for I2C_GPIOInit
    system.o(i.systemInit) refers to mpu6050.o(i.MPU6050_initialize) for MPU6050_initialize
    system.o(i.systemInit) refers to pstwo.o(i.PS2_Init) for PS2_Init
    system.o(i.systemInit) refers to pstwo.o(i.PS2_SetInit) for PS2_SetInit
    system.o(i.systemInit) refers to timer.o(i.TIM7_Int_Init) for TIM7_Int_Init
    i2c.o(i.I2C_Ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_GPIOInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    i2c.o(i.I2C_GPIOInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    i2c.o(i.I2C_NAck) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_ReadByte) for I2C_ReadByte
    i2c.o(i.I2C_ReadBuff) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_ReadByte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_ReadByte) refers to i2c.o(i.I2C_NAck) for I2C_NAck
    i2c.o(i.I2C_ReadByte) refers to i2c.o(i.I2C_Ack) for I2C_Ack
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_ReadByte) for I2C_ReadByte
    i2c.o(i.I2C_ReadOneByte) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_Start) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_Stop) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WaiteForAck) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WaiteForAck) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_WriteBits) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    i2c.o(i.I2C_WriteBits) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_WriteBuff) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    i2c.o(i.I2C_WriteByte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_WriteOneBit) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    i2c.o(i.I2C_WriteOneBit) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_Start) for I2C_Start
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_WaiteForAck) for I2C_WaiteForAck
    i2c.o(i.I2C_WriteOneByte) refers to i2c.o(i.I2C_Stop) for I2C_Stop
    mpu6050.o(i.MPU6050_Set_LPF) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_Set_Rate) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_Set_Rate) refers to mpu6050.o(i.MPU6050_Set_LPF) for MPU6050_Set_LPF
    mpu6050.o(i.MPU6050_getDeviceID) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU6050_initialize) refers to i2c.o(i.I2C_WriteOneByte) for I2C_WriteOneByte
    mpu6050.o(i.MPU6050_initialize) refers to delay.o(i.delay_ms) for delay_ms
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_setFullScaleGyroRange) for MPU6050_setFullScaleGyroRange
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_setFullScaleAccelRange) for MPU6050_setFullScaleAccelRange
    mpu6050.o(i.MPU6050_initialize) refers to mpu6050.o(i.MPU6050_Set_Rate) for MPU6050_Set_Rate
    mpu6050.o(i.MPU6050_initialize) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU6050_newValues) refers to mpu6050.o(.bss) for .bss
    mpu6050.o(i.MPU6050_setClockSource) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setFullScaleAccelRange) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setFullScaleGyroRange) refers to i2c.o(i.I2C_WriteBits) for I2C_WriteBits
    mpu6050.o(i.MPU6050_setI2CBypassEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_setI2CMasterModeEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_setSleepEnabled) refers to i2c.o(i.I2C_WriteOneBit) for I2C_WriteOneBit
    mpu6050.o(i.MPU6050_task) refers to mpu6050.o(i.MPU_Get_Gyroscope) for MPU_Get_Gyroscope
    mpu6050.o(i.MPU6050_task) refers to mpu6050.o(i.MPU_Get_Accelscope) for MPU_Get_Accelscope
    mpu6050.o(i.MPU6050_testConnection) refers to mpu6050.o(i.MPU6050_getDeviceID) for MPU6050_getDeviceID
    mpu6050.o(i.MPU_Get_Accelscope) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU_Get_Accelscope) refers to mpu6050.o(.data) for .data
    mpu6050.o(i.MPU_Get_Gyroscope) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(.data) for .data
    mpu6050.o(i.Read_Temperature) refers to i2c.o(i.I2C_ReadOneByte) for I2C_ReadOneByte
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    tan.o(i.__hardfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__hardfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__hardfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__hardfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.__softfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__softfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____hardfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____hardfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____hardfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____hardfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____hardfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i.o(i.__kernel_tan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to fabs.o(i.fabs) for fabs
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to fabs.o(i.fabs) for fabs
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.constdata), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(i.CAN1_Msg_Pend), (32 bytes).
    Removing can.o(i.CAN1_Receive_Msg), (66 bytes).
    Removing can.o(i.CAN1_Send_Msg), (58 bytes).
    Removing can.o(i.CAN1_Send_MsgTEST), (58 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.Delay_ms), (18 bytes).
    Removing key.o(i.Long_Press), (60 bytes).
    Removing key.o(i.click), (48 bytes).
    Removing key.o(i.click_N_Double), (140 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.data), (4 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_Display_Off), (30 bytes).
    Removing oled.o(i.OLED_Display_On), (30 bytes).
    Removing oled.o(i.OLED_Set_Pos), (40 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (84 bytes).
    Removing pstwo.o(.rev16_text), (4 bytes).
    Removing pstwo.o(.revsh_text), (4 bytes).
    Removing pstwo.o(i.PS2_AnologData), (12 bytes).
    Removing pstwo.o(i.PS2_Receive), (124 bytes).
    Removing pstwo.o(i.PS2_RedLight), (60 bytes).
    Removing pstwo.o(i.PS2_Vibration), (96 bytes).
    Removing pstwo.o(i.PS2_VibrationMode), (68 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(i.TIM8_SERVO_Init), (244 bytes).
    Removing usartx.o(.rev16_text), (4 bytes).
    Removing usartx.o(.revsh_text), (4 bytes).
    Removing usartx.o(i.usart2_send), (20 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.uart_init), (172 bytes).
    Removing usart.o(.bss), (200 bytes).
    Removing usart.o(.data), (2 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (260 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (24 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (40 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (48 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (48 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (20 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (28 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (172 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (30 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (32 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (36 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (20 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (44 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (204 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (168 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (232 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (140 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (142 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (90 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (164 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (108 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (336 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (74 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_LowRegulatorLowVoltageCmd), (16 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorLowVoltageCmd), (16 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (28 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing balance.o(.rev16_text), (4 bytes).
    Removing balance.o(.revsh_text), (4 bytes).
    Removing balance.o(i.myabs), (8 bytes).
    Removing balance.o(i.robot_mode_check), (160 bytes).
    Removing balance.o(.data), (2 bytes).
    Removing datascope_dp.o(i.DataScope_Data_Generate), (128 bytes).
    Removing datascope_dp.o(i.DataScope_Get_Channel_Data), (88 bytes).
    Removing datascope_dp.o(i.Float2Byte), (20 bytes).
    Removing datascope_dp.o(.bss), (42 bytes).
    Removing filter.o(.rev16_text), (4 bytes).
    Removing filter.o(.revsh_text), (4 bytes).
    Removing filter.o(i.Kalman_Filter), (260 bytes).
    Removing filter.o(i.Yijielvbo), (132 bytes).
    Removing filter.o(.bss), (16 bytes).
    Removing filter.o(.data), (84 bytes).
    Removing robot_select_init.o(.rev16_text), (4 bytes).
    Removing robot_select_init.o(.revsh_text), (4 bytes).
    Removing show.o(.rev16_text), (4 bytes).
    Removing show.o(.revsh_text), (4 bytes).
    Removing show.o(.data), (4 bytes).
    Removing show.o(.data), (1 bytes).
    Removing show.o(.data), (1 bytes).
    Removing system.o(.rev16_text), (4 bytes).
    Removing system.o(.revsh_text), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing system.o(.data), (4 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(i.I2C_ReadBuff), (100 bytes).
    Removing i2c.o(i.I2C_WriteBuff), (76 bytes).
    Removing i2c.o(i.I2C_WriteOneBit), (46 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(i.MPU6050_getDeviceID), (8 bytes).
    Removing mpu6050.o(i.MPU6050_newValues), (260 bytes).
    Removing mpu6050.o(i.MPU6050_setClockSource), (18 bytes).
    Removing mpu6050.o(i.MPU6050_setI2CBypassEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_setI2CMasterModeEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_setSleepEnabled), (12 bytes).
    Removing mpu6050.o(i.MPU6050_testConnection), (18 bytes).
    Removing mpu6050.o(i.Read_Temperature), (96 bytes).
    Removing mpu6050.o(.bss), (14 bytes).
    Removing mpu6050.o(.bss), (132 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (4 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (2 bytes).
    Removing mpu6050.o(.data), (4 bytes).

350 unused section(s) (total 10136 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan_x.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i_x.o ABSOLUTE
    ..\BALANCE\DataScope_DP.C                0x00000000   Number         0  datascope_dp.o ABSOLUTE
    ..\BALANCE\balance.c                     0x00000000   Number         0  balance.o ABSOLUTE
    ..\BALANCE\filter.c                      0x00000000   Number         0  filter.o ABSOLUTE
    ..\BALANCE\robot_select_init.c           0x00000000   Number         0  robot_select_init.o ABSOLUTE
    ..\BALANCE\show.c                        0x00000000   Number         0  show.o ABSOLUTE
    ..\BALANCE\system.c                      0x00000000   Number         0  system.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\LED.C                        0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MPU6050\I2C.c                0x00000000   Number         0  i2c.o ABSOLUTE
    ..\HARDWARE\MPU6050\MPU6050.c            0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\HARDWARE\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ..\HARDWARE\encoder.c                    0x00000000   Number         0  encoder.o ABSOLUTE
    ..\HARDWARE\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\motor.c                      0x00000000   Number         0  motor.o ABSOLUTE
    ..\HARDWARE\oled.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\pstwo.c                      0x00000000   Number         0  pstwo.o ABSOLUTE
    ..\HARDWARE\timer.c                      0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\usartx.c                     0x00000000   Number         0  usartx.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\BALANCE\\balance.c                   0x00000000   Number         0  balance.o ABSOLUTE
    ..\\BALANCE\\filter.c                    0x00000000   Number         0  filter.o ABSOLUTE
    ..\\BALANCE\\robot_select_init.c         0x00000000   Number         0  robot_select_init.o ABSOLUTE
    ..\\BALANCE\\show.c                      0x00000000   Number         0  show.o ABSOLUTE
    ..\\BALANCE\\system.c                    0x00000000   Number         0  system.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\LED.C                      0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\I2C.c             0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\MPU6050.c         0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\HARDWARE\\adc.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HARDWARE\\can.c                      0x00000000   Number         0  can.o ABSOLUTE
    ..\\HARDWARE\\encoder.c                  0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\HARDWARE\\key.c                      0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\motor.c                    0x00000000   Number         0  motor.o ABSOLUTE
    ..\\HARDWARE\\oled.c                     0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\pstwo.c                    0x00000000   Number         0  pstwo.o ABSOLUTE
    ..\\HARDWARE\\timer.c                    0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\usartx.c                   0x00000000   Number         0  usartx.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x0800023c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000242   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000246   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000248   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800024c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800024c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800024e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000250   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000250   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000252   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000252   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000252   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000258   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800025c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800025c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000264   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000266   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000266   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800026a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000270   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000270   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080002b0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002b4   Section        0  noretval__2printf.o(.text)
    .text                                    0x080002cc   Section        0  __printf.o(.text)
    .text                                    0x08000334   Section        0  _printf_dec.o(.text)
    .text                                    0x080003ac   Section       68  rt_memclr.o(.text)
    .text                                    0x080003f0   Section        0  heapauxi.o(.text)
    .text                                    0x080003f6   Section        2  use_no_semi.o(.text)
    .text                                    0x080003f8   Section        0  _rserrno.o(.text)
    .text                                    0x0800040e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080004c0   Section        0  _printf_char_file.o(.text)
    .text                                    0x080004e4   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000534   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800053c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800053d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800056c   Section        0  ferror.o(.text)
    .text                                    0x08000574   Section        8  libspace.o(.text)
    .text                                    0x0800057c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080005c6   Section        0  exit.o(.text)
    i.ADC_Cmd                                0x080005d8   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x080005ec   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_GetConversionValue                 0x08000618   Section        0  stm32f4xx_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x0800061e   Section        0  stm32f4xx_adc.o(i.ADC_GetFlagStatus)
    i.ADC_Init                               0x0800062c   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08000678   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_SoftwareStartConv                  0x080006ec   Section        0  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    i.APP_Show                               0x080006f8   Section        0  show.o(i.APP_Show)
    i.Adc_Init                               0x08000830   Section        0  adc.o(i.Adc_Init)
    i.Adc_POWER_Init                         0x080008b0   Section        0  adc.o(i.Adc_POWER_Init)
    i.BusFault_Handler                       0x08000930   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Buzzer_Init                            0x08000934   Section        0  led.o(i.Buzzer_Init)
    i.CAN1_Mode_Init                         0x08000968   Section        0  can.o(i.CAN1_Mode_Init)
    i.CAN1_RX0_IRQHandler                    0x08000b00   Section        0  can.o(i.CAN1_RX0_IRQHandler)
    i.CAN1_Rx_Msg                            0x08000bf0   Section        0  can.o(i.CAN1_Rx_Msg)
    i.CAN1_Send_Num                          0x08000c84   Section        0  can.o(i.CAN1_Send_Num)
    i.CAN1_Tx_Msg                            0x08000cbc   Section        0  can.o(i.CAN1_Tx_Msg)
    i.CAN1_Tx_Staus                          0x08000d68   Section        0  can.o(i.CAN1_Tx_Staus)
    i.CAN_ITConfig                           0x08000dcc   Section        0  stm32f4xx_can.o(i.CAN_ITConfig)
    i.CAN_SEND                               0x08000ddc   Section        0  usartx.o(i.CAN_SEND)
    i.Check_Sum                              0x08000e34   Section        0  usartx.o(i.Check_Sum)
    i.DebugMon_Handler                       0x08000e6c   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Drive_Motor                            0x08000e70   Section        0  balance.o(i.Drive_Motor)
    i.Enable_Pin                             0x08001328   Section        0  motor.o(i.Enable_Pin)
    i.Encoder_Init_TIM2                      0x08001358   Section        0  encoder.o(i.Encoder_Init_TIM2)
    i.Encoder_Init_TIM3                      0x08001434   Section        0  encoder.o(i.Encoder_Init_TIM3)
    i.Encoder_Init_TIM4                      0x080014f0   Section        0  encoder.o(i.Encoder_Init_TIM4)
    i.Encoder_Init_TIM5                      0x080015ac   Section        0  encoder.o(i.Encoder_Init_TIM5)
    i.GPIO_Init                              0x08001668   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x080016e0   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_SetBits                           0x08001700   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.Get_Adc                                0x08001704   Section        0  adc.o(i.Get_Adc)
    i.Get_Adc2                               0x08001734   Section        0  adc.o(i.Get_Adc2)
    i.Get_RC                                 0x08001764   Section        0  balance.o(i.Get_RC)
    i.Get_Velocity_Form_Encoder              0x0800192c   Section        0  balance.o(i.Get_Velocity_Form_Encoder)
    i.Get_adc_Average                        0x08001a3c   Section        0  adc.o(i.Get_adc_Average)
    i.Get_battery_volt                       0x08001a6c   Section        0  adc.o(i.Get_battery_volt)
    i.HardFault_Handler                      0x08001ad4   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Ack                                0x08001ad8   Section        0  i2c.o(i.I2C_Ack)
    i.I2C_GPIOInit                           0x08001b18   Section        0  i2c.o(i.I2C_GPIOInit)
    i.I2C_NAck                               0x08001b5c   Section        0  i2c.o(i.I2C_NAck)
    i.I2C_ReadByte                           0x08001b9c   Section        0  i2c.o(i.I2C_ReadByte)
    i.I2C_ReadOneByte                        0x08001c0c   Section        0  i2c.o(i.I2C_ReadOneByte)
    i.I2C_Start                              0x08001c4c   Section        0  i2c.o(i.I2C_Start)
    i.I2C_Stop                               0x08001c9c   Section        0  i2c.o(i.I2C_Stop)
    i.I2C_WaiteForAck                        0x08001cdc   Section        0  i2c.o(i.I2C_WaiteForAck)
    i.I2C_WriteBits                          0x08001d30   Section        0  i2c.o(i.I2C_WriteBits)
    i.I2C_WriteByte                          0x08001d7c   Section        0  i2c.o(i.I2C_WriteByte)
    i.I2C_WriteOneByte                       0x08001ddc   Section        0  i2c.o(i.I2C_WriteOneByte)
    i.Incremental_PI_A                       0x08001e10   Section        0  balance.o(i.Incremental_PI_A)
    i.Incremental_PI_B                       0x08001e8c   Section        0  balance.o(i.Incremental_PI_B)
    i.Incremental_PI_C                       0x08001f08   Section        0  balance.o(i.Incremental_PI_C)
    i.Incremental_PI_D                       0x08001f84   Section        0  balance.o(i.Incremental_PI_D)
    i.KEY_Init                               0x08002000   Section        0  key.o(i.KEY_Init)
    i.Key                                    0x0800202c   Section        0  balance.o(i.Key)
    i.LED_Init                               0x08002050   Section        0  led.o(i.LED_Init)
    i.Led_Flash                              0x08002090   Section        0  led.o(i.Led_Flash)
    i.Limit_Pwm                              0x080020c0   Section        0  balance.o(i.Limit_Pwm)
    i.MPU6050_Set_LPF                        0x08002150   Section        0  mpu6050.o(i.MPU6050_Set_LPF)
    i.MPU6050_Set_Rate                       0x08002182   Section        0  mpu6050.o(i.MPU6050_Set_Rate)
    i.MPU6050_initialize                     0x080021b2   Section        0  mpu6050.o(i.MPU6050_initialize)
    i.MPU6050_setFullScaleAccelRange         0x08002236   Section        0  mpu6050.o(i.MPU6050_setFullScaleAccelRange)
    i.MPU6050_setFullScaleGyroRange          0x08002248   Section        0  mpu6050.o(i.MPU6050_setFullScaleGyroRange)
    i.MPU6050_task                           0x0800225a   Section        0  mpu6050.o(i.MPU6050_task)
    i.MPU_Get_Accelscope                     0x08002268   Section        0  mpu6050.o(i.MPU_Get_Accelscope)
    i.MPU_Get_Gyroscope                      0x080022bc   Section        0  mpu6050.o(i.MPU_Get_Gyroscope)
    i.MemManage_Handler                      0x08002310   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002312   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08002314   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08002378   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x0800238c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_DrawPoint                         0x080023b8   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x080023f4   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh_Gram                      0x08002524   Section        0  oled.o(i.OLED_Refresh_Gram)
    i.OLED_ShowChar                          0x0800256c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowNumber                        0x080025f0   Section        0  oled.o(i.OLED_ShowNumber)
    i.OLED_ShowString                        0x08002668   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x080026a8   Section        0  oled.o(i.OLED_WR_Byte)
    i.PS2_ClearData                          0x080026ec   Section        0  pstwo.o(i.PS2_ClearData)
    i.PS2_Cmd                                0x08002704   Section        0  pstwo.o(i.PS2_Cmd)
    i.PS2_DataKey                            0x08002788   Section        0  pstwo.o(i.PS2_DataKey)
    i.PS2_EnterConfing                       0x080027c8   Section        0  pstwo.o(i.PS2_EnterConfing)
    i.PS2_ExitConfing                        0x08002824   Section        0  pstwo.o(i.PS2_ExitConfing)
    i.PS2_Init                               0x08002880   Section        0  pstwo.o(i.PS2_Init)
    i.PS2_Read                               0x080028d4   Section        0  pstwo.o(i.PS2_Read)
    i.PS2_ReadData                           0x080029ac   Section        0  pstwo.o(i.PS2_ReadData)
    i.PS2_SetInit                            0x08002a54   Section        0  pstwo.o(i.PS2_SetInit)
    i.PS2_ShortPoll                          0x08002a74   Section        0  pstwo.o(i.PS2_ShortPoll)
    i.PS2_TurnOnAnalogMode                   0x08002ab8   Section        0  pstwo.o(i.PS2_TurnOnAnalogMode)
    i.PS2_control                            0x08002b10   Section        0  balance.o(i.PS2_control)
    i.PWR_BackupAccessCmd                    0x08002cac   Section        0  stm32f4xx_pwr.o(i.PWR_BackupAccessCmd)
    i.PendSV_Handler                         0x08002cb8   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x08002cbc   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08002cd4   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08002cec   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08002d04   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x08002d1c   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.RCC_LSEConfig                          0x08002dac   Section        0  stm32f4xx_rcc.o(i.RCC_LSEConfig)
    i.Read_Encoder                           0x08002dc8   Section        0  encoder.o(i.Read_Encoder)
    i.Remote_Control                         0x08002e08   Section        0  balance.o(i.Remote_Control)
    i.Robot_Init                             0x08002fd0   Section        0  robot_select_init.o(i.Robot_Init)
    i.Robot_Select                           0x08003068   Section        0  robot_select_init.o(i.Robot_Select)
    i.SVC_Handler                            0x080031c8   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080031cc   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080031cd   Thumb Code   162  system_stm32f4xx.o(i.SetSysClock)
    i.Set_Pwm                                0x08003280   Section        0  balance.o(i.Set_Pwm)
    i.Smooth_control                         0x080032f4   Section        0  balance.o(i.Smooth_control)
    i.SysTick_CLKSourceConfig                0x08003434   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x0800344c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08003450   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TI1_Config                             0x080034ac   Section        0  stm32f4xx_tim.o(i.TI1_Config)
    TI1_Config                               0x080034ad   Thumb Code    46  stm32f4xx_tim.o(i.TI1_Config)
    i.TI2_Config                             0x080034da   Section        0  stm32f4xx_tim.o(i.TI2_Config)
    TI2_Config                               0x080034db   Thumb Code    54  stm32f4xx_tim.o(i.TI2_Config)
    i.TIM10_PWM_Init                         0x08003510   Section        0  motor.o(i.TIM10_PWM_Init)
    i.TIM11_PWM_Init                         0x080035b0   Section        0  motor.o(i.TIM11_PWM_Init)
    i.TIM12_SERVO_Init                       0x08003658   Section        0  timer.o(i.TIM12_SERVO_Init)
    i.TIM1_PWM_Init                          0x08003728   Section        0  motor.o(i.TIM1_PWM_Init)
    i.TIM2_IRQHandler                        0x08003820   Section        0  encoder.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08003830   Section        0  encoder.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08003844   Section        0  encoder.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x08003858   Section        0  encoder.o(i.TIM5_IRQHandler)
    i.TIM7_IRQHandler                        0x0800386c   Section        0  balance.o(i.TIM7_IRQHandler)
    i.TIM7_Int_Init                          0x08003a54   Section        0  timer.o(i.TIM7_Int_Init)
    i.TIM8_BRK_TIM12_IRQHandler              0x08003ab0   Section        0  encoder.o(i.TIM8_BRK_TIM12_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08003ac4   Section        0  timer.o(i.TIM8_CC_IRQHandler)
    i.TIM8_Cap_Init                          0x08003d1c   Section        0  timer.o(i.TIM8_Cap_Init)
    i.TIM8_UP_TIM13_IRQHandler               0x08003e58   Section        0  timer.o(i.TIM8_UP_TIM13_IRQHandler)
    i.TIM9_PWM_Init                          0x08003e68   Section        0  motor.o(i.TIM9_PWM_Init)
    i.TIM_ARRPreloadConfig                   0x08003f20   Section        0  stm32f4xx_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearFlag                          0x08003f34   Section        0  stm32f4xx_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x08003f3a   Section        0  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08003f40   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08003f54   Section        0  stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_EncoderInterfaceConfig             0x08003f6a   Section        0  stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig)
    i.TIM_GetCapture1                        0x08003f9c   Section        0  stm32f4xx_tim.o(i.TIM_GetCapture1)
    i.TIM_GetCapture2                        0x08003fa0   Section        0  stm32f4xx_tim.o(i.TIM_GetCapture2)
    i.TIM_GetCapture3                        0x08003fa4   Section        0  stm32f4xx_tim.o(i.TIM_GetCapture3)
    i.TIM_GetCapture4                        0x08003fa8   Section        0  stm32f4xx_tim.o(i.TIM_GetCapture4)
    i.TIM_GetITStatus                        0x08003fac   Section        0  stm32f4xx_tim.o(i.TIM_GetITStatus)
    i.TIM_ICInit                             0x08003fc4   Section        0  stm32f4xx_tim.o(i.TIM_ICInit)
    i.TIM_ICStructInit                       0x08004070   Section        0  stm32f4xx_tim.o(i.TIM_ICStructInit)
    i.TIM_ITConfig                           0x08004080   Section        0  stm32f4xx_tim.o(i.TIM_ITConfig)
    i.TIM_OC1Init                            0x08004090   Section        0  stm32f4xx_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PolarityConfig                  0x080040f0   Section        0  stm32f4xx_tim.o(i.TIM_OC1PolarityConfig)
    i.TIM_OC1PreloadConfig                   0x080040fc   Section        0  stm32f4xx_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC2Init                            0x08004108   Section        0  stm32f4xx_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PolarityConfig                  0x08004188   Section        0  stm32f4xx_tim.o(i.TIM_OC2PolarityConfig)
    i.TIM_OC2PreloadConfig                   0x0800419c   Section        0  stm32f4xx_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_OC3Init                            0x080041b0   Section        0  stm32f4xx_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PolarityConfig                  0x0800422c   Section        0  stm32f4xx_tim.o(i.TIM_OC3PolarityConfig)
    i.TIM_OC3PreloadConfig                   0x08004240   Section        0  stm32f4xx_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x0800424c   Section        0  stm32f4xx_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PolarityConfig                  0x080042ac   Section        0  stm32f4xx_tim.o(i.TIM_OC4PolarityConfig)
    i.TIM_OC4PreloadConfig                   0x080042c0   Section        0  stm32f4xx_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_SetCounter                         0x080042d4   Section        0  stm32f4xx_tim.o(i.TIM_SetCounter)
    i.TIM_SetIC1Prescaler                    0x080042d8   Section        0  stm32f4xx_tim.o(i.TIM_SetIC1Prescaler)
    i.TIM_SetIC2Prescaler                    0x080042e8   Section        0  stm32f4xx_tim.o(i.TIM_SetIC2Prescaler)
    i.TIM_SetIC3Prescaler                    0x08004300   Section        0  stm32f4xx_tim.o(i.TIM_SetIC3Prescaler)
    i.TIM_SetIC4Prescaler                    0x08004310   Section        0  stm32f4xx_tim.o(i.TIM_SetIC4Prescaler)
    i.TIM_TimeBaseInit                       0x08004328   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.TIM_TimeBaseStructInit                 0x080043a4   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseStructInit)
    i.Turn_Off                               0x080043b8   Section        0  balance.o(i.Turn_Off)
    i.UART5_IRQHandler                       0x08004408   Section        0  usartx.o(i.UART5_IRQHandler)
    i.USART1_IRQHandler                      0x080044f4   Section        0  usartx.o(i.USART1_IRQHandler)
    i.USART1_SEND                            0x080045e4   Section        0  usartx.o(i.USART1_SEND)
    i.USART2_IRQHandler                      0x08004600   Section        0  usartx.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08004824   Section        0  usartx.o(i.USART3_IRQHandler)
    i.USART3_SEND                            0x08004908   Section        0  usartx.o(i.USART3_SEND)
    i.USART5_SEND                            0x08004924   Section        0  usartx.o(i.USART5_SEND)
    i.USART_Cmd                              0x08004940   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08004954   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08004992   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x080049c4   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08004a70   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08004a78   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.Vz_to_Akm_Angle                        0x08004a7c   Section        0  usartx.o(i.Vz_to_Akm_Angle)
    i.XYZ_Target_Speed_transition            0x08004b28   Section        0  usartx.o(i.XYZ_Target_Speed_transition)
    i.__ARM_fpclassify                       0x08004b7c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08004bb0   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_pow                           0x08004e88   Section        0  pow.o(i.__hardfp_pow)
    i.__hardfp_sqrt                          0x08005ad8   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__hardfp_tan                           0x08005b58   Section        0  tan.o(i.__hardfp_tan)
    i.__ieee754_rem_pio2                     0x08005bd8   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_poly                          0x08006010   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_tan                           0x08006108   Section        0  tan_i.o(i.__kernel_tan)
    i.__mathlib_dbl_divzero                  0x08006458   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x08006488   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0800649c   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080064b0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x080064d0   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080064f0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._sys_exit                              0x08006510   Section        0  usart.o(i._sys_exit)
    i.click_N_Double_MPU6050                 0x08006514   Section        0  key.o(i.click_N_Double_MPU6050)
    i.data_task                              0x080065a0   Section        0  usartx.o(i.data_task)
    i.data_transition                        0x080065c4   Section        0  usartx.o(i.data_transition)
    i.delay_init                             0x08006860   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800688c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080068c0   Section        0  delay.o(i.delay_us)
    i.delay_xms                              0x080068f4   Section        0  delay.o(i.delay_xms)
    i.fabs                                   0x08006928   Section        0  fabs.o(i.fabs)
    i.float_abs                              0x08006940   Section        0  balance.o(i.float_abs)
    i.fputc                                  0x08006950   Section        0  usart.o(i.fputc)
    i.main                                   0x08006968   Section        0  main.o(i.main)
    i.oled_pow                               0x08006988   Section        0  oled.o(i.oled_pow)
    i.oled_show                              0x08006998   Section        0  show.o(i.oled_show)
    i.show_task                              0x0800721c   Section        0  show.o(i.show_task)
    i.sqrt                                   0x08007268   Section        0  sqrt.o(i.sqrt)
    i.systemInit                             0x080072d6   Section        0  system.o(i.systemInit)
    i.target_limit_float                     0x08007398   Section        0  balance.o(i.target_limit_float)
    i.target_limit_int                       0x080073b8   Section        0  balance.o(i.target_limit_int)
    i.uart1_init                             0x080073c8   Section        0  usartx.o(i.uart1_init)
    i.uart2_init                             0x08007474   Section        0  usartx.o(i.uart2_init)
    i.uart3_init                             0x0800751c   Section        0  usartx.o(i.uart3_init)
    i.uart5_init                             0x080075cc   Section        0  usartx.o(i.uart5_init)
    i.usart1_send                            0x080076a8   Section        0  usartx.o(i.usart1_send)
    i.usart3_send                            0x080076b8   Section        0  usartx.o(i.usart3_send)
    i.usart5_send                            0x080076cc   Section        0  usartx.o(i.usart5_send)
    x$fpl$basic                              0x080076dc   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x080076dc   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x080076f4   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x080076f4   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08007758   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08007758   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08007769   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x080078a8   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x080078a8   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x080078b8   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x080078b8   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080078d0   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080078d0   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080078d7   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x08007b80   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x08007b80   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08007bde   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08007bde   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08007c0c   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08007c0c   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08007c34   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08007c34   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08007cac   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08007cac   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08007e00   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08007e00   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08007e9c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08007e9c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08007ea8   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08007ea8   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08007f14   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08007f14   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08007f2c   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08007f2c   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x080080c4   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x080080c4   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080080d5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08008298   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08008298   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x080082ee   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x080082ee   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800837a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800837a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08008384   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08008384   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$retnan                             0x0800838e   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x0800838e   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080083f2   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x080083f2   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x0800844e   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800844e   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800847e   Section     5284  oled.o(.constdata)
    x$fpl$usenofp                            0x0800847e   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08009928   Section      152  atan.o(.constdata)
    atanhi                                   0x08009928   Data          32  atan.o(.constdata)
    atanlo                                   0x08009948   Data          32  atan.o(.constdata)
    aTodd                                    0x08009968   Data          40  atan.o(.constdata)
    aTeven                                   0x08009990   Data          48  atan.o(.constdata)
    .constdata                               0x080099c0   Section      136  pow.o(.constdata)
    bp                                       0x080099c0   Data          16  pow.o(.constdata)
    dp_h                                     0x080099d0   Data          16  pow.o(.constdata)
    dp_l                                     0x080099e0   Data          16  pow.o(.constdata)
    L                                        0x080099f0   Data          48  pow.o(.constdata)
    P                                        0x08009a20   Data          40  pow.o(.constdata)
    .constdata                               0x08009a48   Section        8  qnan.o(.constdata)
    .constdata                               0x08009a50   Section      200  rred.o(.constdata)
    pio2s                                    0x08009a50   Data          48  rred.o(.constdata)
    twooverpi                                0x08009a80   Data         152  rred.o(.constdata)
    .constdata                               0x08009b18   Section       96  tan_i.o(.constdata)
    Todd                                     0x08009b18   Data          48  tan_i.o(.constdata)
    Teven                                    0x08009b48   Data          48  tan_i.o(.constdata)
    .data                                    0x20000000   Section        4  adc.o(.data)
    .data                                    0x20000004   Section        4  adc.o(.data)
    .data                                    0x20000008   Section       20  key.o(.data)
    flag_key                                 0x20000008   Data           1  key.o(.data)
    flag_key                                 0x20000009   Data           1  key.o(.data)
    count_key                                0x2000000a   Data           1  key.o(.data)
    double_key                               0x2000000b   Data           1  key.o(.data)
    flag_key                                 0x2000000c   Data           1  key.o(.data)
    count_key                                0x2000000d   Data           1  key.o(.data)
    double_key                               0x2000000e   Data           1  key.o(.data)
    count_single                             0x20000010   Data           2  key.o(.data)
    Forever_count                            0x20000012   Data           2  key.o(.data)
    count_single                             0x20000014   Data           2  key.o(.data)
    Forever_count                            0x20000016   Data           2  key.o(.data)
    Long_Press_count                         0x20000018   Data           2  key.o(.data)
    Long_Press                               0x2000001a   Data           2  key.o(.data)
    .data                                    0x2000001c   Section        4  led.o(.data)
    temp                                     0x2000001c   Data           4  led.o(.data)
    .data                                    0x20000020   Section       40  pstwo.o(.data)
    Strat                                    0x20000024   Data           4  pstwo.o(.data)
    .data                                    0x20000048   Section       68  timer.o(.data)
    .data                                    0x2000008c   Section       16  usartx.o(.data)
    Count                                    0x2000008c   Data           1  usartx.o(.data)
    Flag_PID                                 0x2000008d   Data           1  usartx.o(.data)
    i                                        0x2000008e   Data           1  usartx.o(.data)
    j                                        0x2000008f   Data           1  usartx.o(.data)
    Last_Usart_Receive                       0x20000090   Data           1  usartx.o(.data)
    Count                                    0x20000091   Data           1  usartx.o(.data)
    Count                                    0x20000092   Data           1  usartx.o(.data)
    Data                                     0x20000098   Data           4  usartx.o(.data)
    .data                                    0x2000009c   Section        4  delay.o(.data)
    fac_us                                   0x2000009c   Data           1  delay.o(.data)
    fac_ms                                   0x2000009e   Data           2  delay.o(.data)
    .data                                    0x200000a0   Section        4  usart.o(.data)
    .data                                    0x200000a4   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200000a4   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x200000b4   Section        4  balance.o(.data)
    .data                                    0x200000b8   Section       60  balance.o(.data)
    thrice                                   0x200000b8   Data           1  balance.o(.data)
    error                                    0x200000b9   Data           1  balance.o(.data)
    data_sent_count                          0x200000c0   Data           4  balance.o(.data)
    Bias                                     0x200000c4   Data           4  balance.o(.data)
    Pwm                                      0x200000c8   Data           4  balance.o(.data)
    Last_bias                                0x200000cc   Data           4  balance.o(.data)
    Bias                                     0x200000d0   Data           4  balance.o(.data)
    Pwm                                      0x200000d4   Data           4  balance.o(.data)
    Last_bias                                0x200000d8   Data           4  balance.o(.data)
    Bias                                     0x200000dc   Data           4  balance.o(.data)
    Pwm                                      0x200000e0   Data           4  balance.o(.data)
    Last_bias                                0x200000e4   Data           4  balance.o(.data)
    Bias                                     0x200000e8   Data           4  balance.o(.data)
    Pwm                                      0x200000ec   Data           4  balance.o(.data)
    Last_bias                                0x200000f0   Data           4  balance.o(.data)
    .data                                    0x200000f4   Section       12  show.o(.data)
    flag_show                                0x200000f4   Data           1  show.o(.data)
    count                                    0x200000f8   Data           4  show.o(.data)
    .data                                    0x20000100   Section        1  system.o(.data)
    .data                                    0x20000101   Section        1  system.o(.data)
    .data                                    0x20000104   Section        4  system.o(.data)
    .data                                    0x20000108   Section        4  system.o(.data)
    .data                                    0x2000010c   Section        4  system.o(.data)
    .data                                    0x20000110   Section        1  system.o(.data)
    .data                                    0x20000111   Section        1  system.o(.data)
    .data                                    0x20000112   Section        1  system.o(.data)
    .data                                    0x20000113   Section        1  system.o(.data)
    .data                                    0x20000114   Section        1  system.o(.data)
    .data                                    0x20000118   Section        4  system.o(.data)
    .data                                    0x2000011c   Section        4  system.o(.data)
    .data                                    0x20000120   Section        4  system.o(.data)
    .data                                    0x20000124   Section        4  system.o(.data)
    .data                                    0x20000128   Section        4  system.o(.data)
    .data                                    0x2000012c   Section        4  system.o(.data)
    .data                                    0x20000130   Section        4  system.o(.data)
    .data                                    0x20000134   Section        4  system.o(.data)
    .data                                    0x20000138   Section        4  system.o(.data)
    .data                                    0x2000013c   Section        4  system.o(.data)
    .data                                    0x20000140   Section        4  system.o(.data)
    .data                                    0x20000144   Section        4  system.o(.data)
    .data                                    0x20000148   Section        4  system.o(.data)
    .data                                    0x2000014c   Section        1  system.o(.data)
    .data                                    0x2000014d   Section        1  system.o(.data)
    .data                                    0x2000014e   Section        1  system.o(.data)
    .data                                    0x2000014f   Section        1  system.o(.data)
    .data                                    0x20000150   Section        1  system.o(.data)
    .data                                    0x20000151   Section        1  system.o(.data)
    .data                                    0x20000154   Section        4  system.o(.data)
    .data                                    0x20000158   Section        4  system.o(.data)
    .data                                    0x2000015c   Section        4  system.o(.data)
    .data                                    0x20000160   Section        4  system.o(.data)
    .data                                    0x20000164   Section        4  system.o(.data)
    .data                                    0x20000168   Section       12  mpu6050.o(.data)
    .data                                    0x20000174   Section        6  mpu6050.o(.data)
    .data                                    0x2000017a   Section        6  mpu6050.o(.data)
    .bss                                     0x20000180   Section     1024  oled.o(.bss)
    .bss                                     0x20000580   Section        9  pstwo.o(.bss)
    .bss                                     0x2000058c   Section      144  usartx.o(.bss)
    rxbuf                                    0x2000058c   Data          11  usartx.o(.bss)
    Receive                                  0x20000597   Data          50  usartx.o(.bss)
    .bss                                     0x2000061c   Section       16  balance.o(.bss)
    .bss                                     0x2000062c   Section       24  robot_select_init.o(.bss)
    .bss                                     0x20000644   Section       12  system.o(.bss)
    .bss                                     0x20000650   Section       20  system.o(.bss)
    .bss                                     0x20000664   Section       20  system.o(.bss)
    .bss                                     0x20000678   Section       20  system.o(.bss)
    .bss                                     0x2000068c   Section       20  system.o(.bss)
    .bss                                     0x200006a0   Section       96  libspace.o(.bss)
    HEAP                                     0x20000700   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20000700   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000900   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000900   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000d00   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x0800023d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000243   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000247   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800024d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800024f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000251   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000253   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000253   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000253   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800025d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800025d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000265   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000267   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000267   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800026b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000271   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800028b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x0800028d   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __use_no_semihosting                     0x080002b1   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x080002b5   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x080002cd   Thumb Code   104  __printf.o(.text)
    _printf_int_dec                          0x08000335   Thumb Code   104  _printf_dec.o(.text)
    __aeabi_memclr                           0x080003ad   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080003ad   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080003b1   Thumb Code     0  rt_memclr.o(.text)
    __use_two_region_memory                  0x080003f1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080003f3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080003f5   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080003f7   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080003f7   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x080003f9   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000403   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800040f   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_file                        0x080004c1   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_memclr4                          0x080004e5   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080004e5   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080004e5   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080004e9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_errno_addr                       0x08000535   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000535   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000535   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x08000547   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x0800056d   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000575   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000575   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000575   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800057d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080005c7   Thumb Code    18  exit.o(.text)
    ADC_Cmd                                  0x080005d9   Thumb Code    20  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x080005ed   Thumb Code    34  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_GetConversionValue                   0x08000619   Thumb Code     6  stm32f4xx_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x0800061f   Thumb Code    14  stm32f4xx_adc.o(i.ADC_GetFlagStatus)
    ADC_Init                                 0x0800062d   Thumb Code    66  stm32f4xx_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08000679   Thumb Code   116  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ADC_SoftwareStartConv                    0x080006ed   Thumb Code    10  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    APP_Show                                 0x080006f9   Thumb Code   214  show.o(i.APP_Show)
    Adc_Init                                 0x08000831   Thumb Code   120  adc.o(i.Adc_Init)
    Adc_POWER_Init                           0x080008b1   Thumb Code   118  adc.o(i.Adc_POWER_Init)
    BusFault_Handler                         0x08000931   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    Buzzer_Init                              0x08000935   Thumb Code    48  led.o(i.Buzzer_Init)
    CAN1_Mode_Init                           0x08000969   Thumb Code   394  can.o(i.CAN1_Mode_Init)
    CAN1_RX0_IRQHandler                      0x08000b01   Thumb Code   196  can.o(i.CAN1_RX0_IRQHandler)
    CAN1_Rx_Msg                              0x08000bf1   Thumb Code   140  can.o(i.CAN1_Rx_Msg)
    CAN1_Send_Num                            0x08000c85   Thumb Code    54  can.o(i.CAN1_Send_Num)
    CAN1_Tx_Msg                              0x08000cbd   Thumb Code   164  can.o(i.CAN1_Tx_Msg)
    CAN1_Tx_Staus                            0x08000d69   Thumb Code    94  can.o(i.CAN1_Tx_Staus)
    CAN_ITConfig                             0x08000dcd   Thumb Code    16  stm32f4xx_can.o(i.CAN_ITConfig)
    CAN_SEND                                 0x08000ddd   Thumb Code    84  usartx.o(i.CAN_SEND)
    Check_Sum                                0x08000e35   Thumb Code    52  usartx.o(i.Check_Sum)
    DebugMon_Handler                         0x08000e6d   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Drive_Motor                              0x08000e71   Thumb Code  1206  balance.o(i.Drive_Motor)
    Enable_Pin                               0x08001329   Thumb Code    42  motor.o(i.Enable_Pin)
    Encoder_Init_TIM2                        0x08001359   Thumb Code   212  encoder.o(i.Encoder_Init_TIM2)
    Encoder_Init_TIM3                        0x08001435   Thumb Code   180  encoder.o(i.Encoder_Init_TIM3)
    Encoder_Init_TIM4                        0x080014f1   Thumb Code   180  encoder.o(i.Encoder_Init_TIM4)
    Encoder_Init_TIM5                        0x080015ad   Thumb Code   178  encoder.o(i.Encoder_Init_TIM5)
    GPIO_Init                                0x08001669   Thumb Code   120  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x080016e1   Thumb Code    32  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_SetBits                             0x08001701   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    Get_Adc                                  0x08001705   Thumb Code    44  adc.o(i.Get_Adc)
    Get_Adc2                                 0x08001735   Thumb Code    44  adc.o(i.Get_Adc2)
    Get_RC                                   0x08001765   Thumb Code   402  balance.o(i.Get_RC)
    Get_Velocity_Form_Encoder                0x0800192d   Thumb Code   236  balance.o(i.Get_Velocity_Form_Encoder)
    Get_adc_Average                          0x08001a3d   Thumb Code    46  adc.o(i.Get_adc_Average)
    Get_battery_volt                         0x08001a6d   Thumb Code    70  adc.o(i.Get_battery_volt)
    HardFault_Handler                        0x08001ad5   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    I2C_Ack                                  0x08001ad9   Thumb Code    54  i2c.o(i.I2C_Ack)
    I2C_GPIOInit                             0x08001b19   Thumb Code    58  i2c.o(i.I2C_GPIOInit)
    I2C_NAck                                 0x08001b5d   Thumb Code    56  i2c.o(i.I2C_NAck)
    I2C_ReadByte                             0x08001b9d   Thumb Code   102  i2c.o(i.I2C_ReadByte)
    I2C_ReadOneByte                          0x08001c0d   Thumb Code    62  i2c.o(i.I2C_ReadOneByte)
    I2C_Start                                0x08001c4d   Thumb Code    70  i2c.o(i.I2C_Start)
    I2C_Stop                                 0x08001c9d   Thumb Code    56  i2c.o(i.I2C_Stop)
    I2C_WaiteForAck                          0x08001cdd   Thumb Code    76  i2c.o(i.I2C_WaiteForAck)
    I2C_WriteBits                            0x08001d31   Thumb Code    74  i2c.o(i.I2C_WriteBits)
    I2C_WriteByte                            0x08001d7d   Thumb Code    86  i2c.o(i.I2C_WriteByte)
    I2C_WriteOneByte                         0x08001ddd   Thumb Code    50  i2c.o(i.I2C_WriteOneByte)
    Incremental_PI_A                         0x08001e11   Thumb Code    96  balance.o(i.Incremental_PI_A)
    Incremental_PI_B                         0x08001e8d   Thumb Code    96  balance.o(i.Incremental_PI_B)
    Incremental_PI_C                         0x08001f09   Thumb Code    96  balance.o(i.Incremental_PI_C)
    Incremental_PI_D                         0x08001f85   Thumb Code    96  balance.o(i.Incremental_PI_D)
    KEY_Init                                 0x08002001   Thumb Code    40  key.o(i.KEY_Init)
    Key                                      0x0800202d   Thumb Code    26  balance.o(i.Key)
    LED_Init                                 0x08002051   Thumb Code    58  led.o(i.LED_Init)
    Led_Flash                                0x08002091   Thumb Code    40  led.o(i.Led_Flash)
    Limit_Pwm                                0x080020c1   Thumb Code   126  balance.o(i.Limit_Pwm)
    MPU6050_Set_LPF                          0x08002151   Thumb Code    50  mpu6050.o(i.MPU6050_Set_LPF)
    MPU6050_Set_Rate                         0x08002183   Thumb Code    48  mpu6050.o(i.MPU6050_Set_Rate)
    MPU6050_initialize                       0x080021b3   Thumb Code   132  mpu6050.o(i.MPU6050_initialize)
    MPU6050_setFullScaleAccelRange           0x08002237   Thumb Code    18  mpu6050.o(i.MPU6050_setFullScaleAccelRange)
    MPU6050_setFullScaleGyroRange            0x08002249   Thumb Code    18  mpu6050.o(i.MPU6050_setFullScaleGyroRange)
    MPU6050_task                             0x0800225b   Thumb Code    14  mpu6050.o(i.MPU6050_task)
    MPU_Get_Accelscope                       0x08002269   Thumb Code    78  mpu6050.o(i.MPU_Get_Accelscope)
    MPU_Get_Gyroscope                        0x080022bd   Thumb Code    78  mpu6050.o(i.MPU_Get_Gyroscope)
    MemManage_Handler                        0x08002311   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002313   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08002315   Thumb Code    96  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08002379   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x0800238d   Thumb Code    38  oled.o(i.OLED_Clear)
    OLED_DrawPoint                           0x080023b9   Thumb Code    54  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x080023f5   Thumb Code   294  oled.o(i.OLED_Init)
    OLED_Refresh_Gram                        0x08002525   Thumb Code    66  oled.o(i.OLED_Refresh_Gram)
    OLED_ShowChar                            0x0800256d   Thumb Code   124  oled.o(i.OLED_ShowChar)
    OLED_ShowNumber                          0x080025f1   Thumb Code   120  oled.o(i.OLED_ShowNumber)
    OLED_ShowString                          0x08002669   Thumb Code    62  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x080026a9   Thumb Code    62  oled.o(i.OLED_WR_Byte)
    PS2_ClearData                            0x080026ed   Thumb Code    18  pstwo.o(i.PS2_ClearData)
    PS2_Cmd                                  0x08002705   Thumb Code   124  pstwo.o(i.PS2_Cmd)
    PS2_DataKey                              0x08002789   Thumb Code    56  pstwo.o(i.PS2_DataKey)
    PS2_EnterConfing                         0x080027c9   Thumb Code    88  pstwo.o(i.PS2_EnterConfing)
    PS2_ExitConfing                          0x08002825   Thumb Code    88  pstwo.o(i.PS2_ExitConfing)
    PS2_Init                                 0x08002881   Thumb Code    78  pstwo.o(i.PS2_Init)
    PS2_Read                                 0x080028d5   Thumb Code   158  pstwo.o(i.PS2_Read)
    PS2_ReadData                             0x080029ad   Thumb Code   156  pstwo.o(i.PS2_ReadData)
    PS2_SetInit                              0x08002a55   Thumb Code    30  pstwo.o(i.PS2_SetInit)
    PS2_ShortPoll                            0x08002a75   Thumb Code    64  pstwo.o(i.PS2_ShortPoll)
    PS2_TurnOnAnalogMode                     0x08002ab9   Thumb Code    82  pstwo.o(i.PS2_TurnOnAnalogMode)
    PS2_control                              0x08002b11   Thumb Code   344  balance.o(i.PS2_control)
    PWR_BackupAccessCmd                      0x08002cad   Thumb Code     6  stm32f4xx_pwr.o(i.PWR_BackupAccessCmd)
    PendSV_Handler                           0x08002cb9   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x08002cbd   Thumb Code    18  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08002cd5   Thumb Code    18  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08002ced   Thumb Code    18  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08002d05   Thumb Code    18  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x08002d1d   Thumb Code   132  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    RCC_LSEConfig                            0x08002dad   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_LSEConfig)
    Read_Encoder                             0x08002dc9   Thumb Code    52  encoder.o(i.Read_Encoder)
    Remote_Control                           0x08002e09   Thumb Code   398  balance.o(i.Remote_Control)
    Robot_Init                               0x08002fd1   Thumb Code   124  robot_select_init.o(i.Robot_Init)
    Robot_Select                             0x08003069   Thumb Code   250  robot_select_init.o(i.Robot_Select)
    SVC_Handler                              0x080031c9   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Set_Pwm                                  0x08003281   Thumb Code    94  balance.o(i.Set_Pwm)
    Smooth_control                           0x080032f5   Thumb Code   308  balance.o(i.Smooth_control)
    SysTick_CLKSourceConfig                  0x08003435   Thumb Code    24  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x0800344d   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08003451   Thumb Code    74  system_stm32f4xx.o(i.SystemInit)
    TIM10_PWM_Init                           0x08003511   Thumb Code   150  motor.o(i.TIM10_PWM_Init)
    TIM11_PWM_Init                           0x080035b1   Thumb Code   158  motor.o(i.TIM11_PWM_Init)
    TIM12_SERVO_Init                         0x08003659   Thumb Code   198  timer.o(i.TIM12_SERVO_Init)
    TIM1_PWM_Init                            0x08003729   Thumb Code   238  motor.o(i.TIM1_PWM_Init)
    TIM2_IRQHandler                          0x08003821   Thumb Code    16  encoder.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08003831   Thumb Code    14  encoder.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08003845   Thumb Code    14  encoder.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x08003859   Thumb Code    14  encoder.o(i.TIM5_IRQHandler)
    TIM7_IRQHandler                          0x0800386d   Thumb Code   418  balance.o(i.TIM7_IRQHandler)
    TIM7_Int_Init                            0x08003a55   Thumb Code    88  timer.o(i.TIM7_Int_Init)
    TIM8_BRK_TIM12_IRQHandler                0x08003ab1   Thumb Code    14  encoder.o(i.TIM8_BRK_TIM12_IRQHandler)
    TIM8_CC_IRQHandler                       0x08003ac5   Thumb Code   566  timer.o(i.TIM8_CC_IRQHandler)
    TIM8_Cap_Init                            0x08003d1d   Thumb Code   306  timer.o(i.TIM8_Cap_Init)
    TIM8_UP_TIM13_IRQHandler                 0x08003e59   Thumb Code    12  timer.o(i.TIM8_UP_TIM13_IRQHandler)
    TIM9_PWM_Init                            0x08003e69   Thumb Code   176  motor.o(i.TIM9_PWM_Init)
    TIM_ARRPreloadConfig                     0x08003f21   Thumb Code    20  stm32f4xx_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearFlag                            0x08003f35   Thumb Code     6  stm32f4xx_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x08003f3b   Thumb Code     6  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08003f41   Thumb Code    20  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08003f55   Thumb Code    22  stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_EncoderInterfaceConfig               0x08003f6b   Thumb Code    50  stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig)
    TIM_GetCapture1                          0x08003f9d   Thumb Code     4  stm32f4xx_tim.o(i.TIM_GetCapture1)
    TIM_GetCapture2                          0x08003fa1   Thumb Code     4  stm32f4xx_tim.o(i.TIM_GetCapture2)
    TIM_GetCapture3                          0x08003fa5   Thumb Code     4  stm32f4xx_tim.o(i.TIM_GetCapture3)
    TIM_GetCapture4                          0x08003fa9   Thumb Code     4  stm32f4xx_tim.o(i.TIM_GetCapture4)
    TIM_GetITStatus                          0x08003fad   Thumb Code    24  stm32f4xx_tim.o(i.TIM_GetITStatus)
    TIM_ICInit                               0x08003fc5   Thumb Code   172  stm32f4xx_tim.o(i.TIM_ICInit)
    TIM_ICStructInit                         0x08004071   Thumb Code    16  stm32f4xx_tim.o(i.TIM_ICStructInit)
    TIM_ITConfig                             0x08004081   Thumb Code    16  stm32f4xx_tim.o(i.TIM_ITConfig)
    TIM_OC1Init                              0x08004091   Thumb Code    88  stm32f4xx_tim.o(i.TIM_OC1Init)
    TIM_OC1PolarityConfig                    0x080040f1   Thumb Code    12  stm32f4xx_tim.o(i.TIM_OC1PolarityConfig)
    TIM_OC1PreloadConfig                     0x080040fd   Thumb Code    12  stm32f4xx_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x08004109   Thumb Code   120  stm32f4xx_tim.o(i.TIM_OC2Init)
    TIM_OC2PolarityConfig                    0x08004189   Thumb Code    20  stm32f4xx_tim.o(i.TIM_OC2PolarityConfig)
    TIM_OC2PreloadConfig                     0x0800419d   Thumb Code    20  stm32f4xx_tim.o(i.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x080041b1   Thumb Code   116  stm32f4xx_tim.o(i.TIM_OC3Init)
    TIM_OC3PolarityConfig                    0x0800422d   Thumb Code    20  stm32f4xx_tim.o(i.TIM_OC3PolarityConfig)
    TIM_OC3PreloadConfig                     0x08004241   Thumb Code    12  stm32f4xx_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x0800424d   Thumb Code    88  stm32f4xx_tim.o(i.TIM_OC4Init)
    TIM_OC4PolarityConfig                    0x080042ad   Thumb Code    20  stm32f4xx_tim.o(i.TIM_OC4PolarityConfig)
    TIM_OC4PreloadConfig                     0x080042c1   Thumb Code    20  stm32f4xx_tim.o(i.TIM_OC4PreloadConfig)
    TIM_SetCounter                           0x080042d5   Thumb Code     4  stm32f4xx_tim.o(i.TIM_SetCounter)
    TIM_SetIC1Prescaler                      0x080042d9   Thumb Code    16  stm32f4xx_tim.o(i.TIM_SetIC1Prescaler)
    TIM_SetIC2Prescaler                      0x080042e9   Thumb Code    24  stm32f4xx_tim.o(i.TIM_SetIC2Prescaler)
    TIM_SetIC3Prescaler                      0x08004301   Thumb Code    16  stm32f4xx_tim.o(i.TIM_SetIC3Prescaler)
    TIM_SetIC4Prescaler                      0x08004311   Thumb Code    24  stm32f4xx_tim.o(i.TIM_SetIC4Prescaler)
    TIM_TimeBaseInit                         0x08004329   Thumb Code    96  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    TIM_TimeBaseStructInit                   0x080043a5   Thumb Code    18  stm32f4xx_tim.o(i.TIM_TimeBaseStructInit)
    Turn_Off                                 0x080043b9   Thumb Code    54  balance.o(i.Turn_Off)
    UART5_IRQHandler                         0x08004409   Thumb Code   182  usartx.o(i.UART5_IRQHandler)
    USART1_IRQHandler                        0x080044f5   Thumb Code   190  usartx.o(i.USART1_IRQHandler)
    USART1_SEND                              0x080045e5   Thumb Code    22  usartx.o(i.USART1_SEND)
    USART2_IRQHandler                        0x08004601   Thumb Code   464  usartx.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08004825   Thumb Code   176  usartx.o(i.USART3_IRQHandler)
    USART3_SEND                              0x08004909   Thumb Code    22  usartx.o(i.USART3_SEND)
    USART5_SEND                              0x08004925   Thumb Code    22  usartx.o(i.USART5_SEND)
    USART_Cmd                                0x08004941   Thumb Code    20  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08004955   Thumb Code    62  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08004993   Thumb Code    48  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x080049c5   Thumb Code   164  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08004a71   Thumb Code     8  stm32f4xx_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08004a79   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    Vz_to_Akm_Angle                          0x08004a7d   Thumb Code   156  usartx.o(i.Vz_to_Akm_Angle)
    XYZ_Target_Speed_transition              0x08004b29   Thumb Code    74  usartx.o(i.XYZ_Target_Speed_transition)
    __ARM_fpclassify                         0x08004b7d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08004bb1   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_pow                             0x08004e89   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __hardfp_sqrt                            0x08005ad9   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __hardfp_tan                             0x08005b59   Thumb Code   108  tan.o(i.__hardfp_tan)
    __ieee754_rem_pio2                       0x08005bd9   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_poly                            0x08006011   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_tan                             0x08006109   Thumb Code   764  tan_i.o(i.__kernel_tan)
    __mathlib_dbl_divzero                    0x08006459   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x08006489   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0800649d   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080064b1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x080064d1   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080064f1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _sys_exit                                0x08006511   Thumb Code     2  usart.o(i._sys_exit)
    click_N_Double_MPU6050                   0x08006515   Thumb Code   132  key.o(i.click_N_Double_MPU6050)
    data_task                                0x080065a1   Thumb Code    30  usartx.o(i.data_task)
    data_transition                          0x080065c5   Thumb Code   586  usartx.o(i.data_transition)
    delay_init                               0x08006861   Thumb Code    38  delay.o(i.delay_init)
    delay_ms                                 0x0800688d   Thumb Code    52  delay.o(i.delay_ms)
    delay_us                                 0x080068c1   Thumb Code    46  delay.o(i.delay_us)
    delay_xms                                0x080068f5   Thumb Code    46  delay.o(i.delay_xms)
    fabs                                     0x08006929   Thumb Code    24  fabs.o(i.fabs)
    float_abs                                0x08006941   Thumb Code    16  balance.o(i.float_abs)
    fputc                                    0x08006951   Thumb Code    18  usart.o(i.fputc)
    main                                     0x08006969   Thumb Code    26  main.o(i.main)
    oled_pow                                 0x08006989   Thumb Code    16  oled.o(i.oled_pow)
    oled_show                                0x08006999   Thumb Code  2044  show.o(i.oled_show)
    show_task                                0x0800721d   Thumb Code    62  show.o(i.show_task)
    sqrt                                     0x08007269   Thumb Code   110  sqrt.o(i.sqrt)
    systemInit                               0x080072d7   Thumb Code   194  system.o(i.systemInit)
    target_limit_float                       0x08007399   Thumb Code    32  balance.o(i.target_limit_float)
    target_limit_int                         0x080073b9   Thumb Code    16  balance.o(i.target_limit_int)
    uart1_init                               0x080073c9   Thumb Code   162  usartx.o(i.uart1_init)
    uart2_init                               0x08007475   Thumb Code   160  usartx.o(i.uart2_init)
    uart3_init                               0x0800751d   Thumb Code   168  usartx.o(i.uart3_init)
    uart5_init                               0x080075cd   Thumb Code   206  usartx.o(i.uart5_init)
    usart1_send                              0x080076a9   Thumb Code    12  usartx.o(i.usart1_send)
    usart3_send                              0x080076b9   Thumb Code    14  usartx.o(i.usart3_send)
    usart5_send                              0x080076cd   Thumb Code    12  usartx.o(i.usart5_send)
    __aeabi_dneg                             0x080076dd   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x080076dd   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x080076e3   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x080076e3   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x080076e9   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x080076ef   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x080076f5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x080076f5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08007759   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08007759   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x080078a9   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x080078b9   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080078d1   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080078d1   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x08007b81   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08007b81   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08007bdf   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08007bdf   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08007c0d   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08007c0d   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08007c35   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08007c35   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08007c97   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08007cad   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08007cad   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08007e01   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08007e9d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08007ea9   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08007ea9   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08007f15   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08007f15   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08007f2d   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x080080c5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080080c5   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08008299   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08008299   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x080082ef   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800837b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08008383   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08008383   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08008385   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __fpl_return_NaN                         0x0800838f   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080083f3   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x0800844f   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0800847e   Number         0  usenofp.o(x$fpl$usenofp)
    oled_asc2_1206                           0x0800847e   Data        1140  oled.o(.constdata)
    oled_asc2_1608                           0x080088f2   Data        1520  oled.o(.constdata)
    Hzk16                                    0x08008ee2   Data        2624  oled.o(.constdata)
    __mathlib_zero                           0x08009a48   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08009b78   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009b98   Number         0  anon$$obj.o(Region$$Table)
    Voltage                                  0x20000000   Data           4  adc.o(.data)
    Voltage_All                              0x20000004   Data           4  adc.o(.data)
    Comd                                     0x20000020   Data           2  pstwo.o(.data)
    Handkey                                  0x20000022   Data           2  pstwo.o(.data)
    MASK                                     0x20000028   Data          32  pstwo.o(.data)
    TIM8CH1_CAPTURE_STA                      0x20000048   Data           1  timer.o(.data)
    TIM8CH2_CAPTURE_STA                      0x20000049   Data           1  timer.o(.data)
    TIM8CH3_CAPTURE_STA                      0x2000004a   Data           1  timer.o(.data)
    TIM8CH4_CAPTURE_STA                      0x2000004b   Data           1  timer.o(.data)
    TIM8CH1_CAPTURE_UPVAL                    0x2000004c   Data           2  timer.o(.data)
    TIM8CH1_CAPTURE_DOWNVAL                  0x2000004e   Data           2  timer.o(.data)
    TIM8CH2_CAPTURE_UPVAL                    0x20000050   Data           2  timer.o(.data)
    TIM8CH2_CAPTURE_DOWNVAL                  0x20000052   Data           2  timer.o(.data)
    TIM8CH3_CAPTURE_UPVAL                    0x20000054   Data           2  timer.o(.data)
    TIM8CH3_CAPTURE_DOWNVAL                  0x20000056   Data           2  timer.o(.data)
    TIM8CH4_CAPTURE_UPVAL                    0x20000058   Data           2  timer.o(.data)
    TIM8CH4_CAPTURE_DOWNVAL                  0x2000005a   Data           2  timer.o(.data)
    Remoter_Ch1                              0x2000005c   Data           4  timer.o(.data)
    Remoter_Ch2                              0x20000060   Data           4  timer.o(.data)
    Remoter_Ch3                              0x20000064   Data           4  timer.o(.data)
    Remoter_Ch4                              0x20000068   Data           4  timer.o(.data)
    L_Remoter_Ch1                            0x2000006c   Data           4  timer.o(.data)
    L_Remoter_Ch2                            0x20000070   Data           4  timer.o(.data)
    L_Remoter_Ch3                            0x20000074   Data           4  timer.o(.data)
    L_Remoter_Ch4                            0x20000078   Data           4  timer.o(.data)
    TIM8_T1                                  0x2000007c   Data           4  timer.o(.data)
    TIM8_T2                                  0x20000080   Data           4  timer.o(.data)
    TIM8_T3                                  0x20000084   Data           4  timer.o(.data)
    TIM8_T4                                  0x20000088   Data           4  timer.o(.data)
    data_sent_flag                           0x20000094   Data           4  usartx.o(.data)
    __stdout                                 0x200000a0   Data           4  usart.o(.data)
    Time_count                               0x200000b4   Data           4  balance.o(.data)
    robot_mode_check_flag                    0x200000bc   Data           4  balance.o(.data)
    Voltage_Show                             0x200000fc   Data           4  show.o(.data)
    Flag_Stop                                0x20000100   Data           1  system.o(.data)
    Car_Mode                                 0x20000101   Data           1  system.o(.data)
    RC_Velocity                              0x20000104   Data           4  system.o(.data)
    Velocity_KP                              0x20000108   Data           4  system.o(.data)
    Velocity_KI                              0x2000010c   Data           4  system.o(.data)
    PS2_ON_Flag                              0x20000110   Data           1  system.o(.data)
    APP_ON_Flag                              0x20000111   Data           1  system.o(.data)
    Remote_ON_Flag                           0x20000112   Data           1  system.o(.data)
    CAN_ON_Flag                              0x20000113   Data           1  system.o(.data)
    Flag_Direction                           0x20000114   Data           1  system.o(.data)
    Check                                    0x20000118   Data           4  system.o(.data)
    CheckPhrase1                             0x2000011c   Data           4  system.o(.data)
    CheckPhrase2                             0x20000120   Data           4  system.o(.data)
    Divisor_Mode                             0x20000124   Data           4  system.o(.data)
    Servo                                    0x20000128   Data           4  system.o(.data)
    Move_X                                   0x2000012c   Data           4  system.o(.data)
    Move_Y                                   0x20000130   Data           4  system.o(.data)
    Move_Z                                   0x20000134   Data           4  system.o(.data)
    Encoder_precision                        0x20000138   Data           4  system.o(.data)
    Wheel_perimeter                          0x2000013c   Data           4  system.o(.data)
    Wheel_spacing                            0x20000140   Data           4  system.o(.data)
    Axle_spacing                             0x20000144   Data           4  system.o(.data)
    Omni_turn_radiaus                        0x20000148   Data           4  system.o(.data)
    Usart1_ON_Flag                           0x2000014c   Data           1  system.o(.data)
    Usart5_ON_Flag                           0x2000014d   Data           1  system.o(.data)
    Flag_Left                                0x2000014e   Data           1  system.o(.data)
    Flag_Right                               0x2000014f   Data           1  system.o(.data)
    Turn_Flag                                0x20000150   Data           1  system.o(.data)
    PID_Send                                 0x20000151   Data           1  system.o(.data)
    PS2_LX                                   0x20000154   Data           4  system.o(.data)
    PS2_LY                                   0x20000158   Data           4  system.o(.data)
    PS2_RX                                   0x2000015c   Data           4  system.o(.data)
    PS2_RY                                   0x20000160   Data           4  system.o(.data)
    PS2_KEY                                  0x20000164   Data           4  system.o(.data)
    gyro                                     0x20000168   Data           6  mpu6050.o(.data)
    accel                                    0x2000016e   Data           6  mpu6050.o(.data)
    Deviation_gyro                           0x20000174   Data           6  mpu6050.o(.data)
    Original_gyro                            0x2000017a   Data           6  mpu6050.o(.data)
    OLED_GRAM                                0x20000180   Data        1024  oled.o(.bss)
    Data                                     0x20000580   Data           9  pstwo.o(.bss)
    Send_Data                                0x200005ca   Data          48  usartx.o(.bss)
    Receive_Data                             0x200005fc   Data          32  usartx.o(.bss)
    OriginalEncoder                          0x2000061c   Data          16  balance.o(.bss)
    Robot_Parament                           0x2000062c   Data          24  robot_select_init.o(.bss)
    smooth_control                           0x20000644   Data          12  system.o(.bss)
    MOTOR_A                                  0x20000650   Data          20  system.o(.bss)
    MOTOR_B                                  0x20000664   Data          20  system.o(.bss)
    MOTOR_C                                  0x20000678   Data          20  system.o(.bss)
    MOTOR_D                                  0x2000068c   Data          20  system.o(.bss)
    __libspace_start                         0x200006a0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000700   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009d18, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00009bdc])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009b98, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO         1217    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         3623  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         3943    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         3941    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         3945    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         3618    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         3617    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000242   0x08000242   0x00000004   Code   RO         3712    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000246   0x08000246   0x00000002   Code   RO         3814    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000248   0x08000248   0x00000004   Code   RO         3819    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3822    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3825    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3827    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3829    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3832    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3834    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3836    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3838    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3840    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3842    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3844    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3846    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3848    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3850    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3852    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3856    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3858    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3860    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO         3862    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO         3863    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800024e   0x0800024e   0x00000002   Code   RO         3883    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3893    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3895    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3898    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3901    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3903    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000000   Code   RO         3906    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000250   0x08000250   0x00000002   Code   RO         3907    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         3705    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000252   0x08000252   0x00000000   Code   RO         3771    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000006   Code   RO         3783    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000258   0x08000258   0x00000000   Code   RO         3773    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000004   Code   RO         3774    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         3776    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800025c   0x0800025c   0x00000008   Code   RO         3777    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000264   0x08000264   0x00000002   Code   RO         3817    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000266   0x08000266   0x00000000   Code   RO         3867    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000266   0x08000266   0x00000004   Code   RO         3868    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         3869    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000270   0x08000270   0x00000040   Code   RO         1218    .text               startup_stm32f40_41xxx.o
    0x080002b0   0x080002b0   0x00000002   Code   RO         3587    .text               c_w.l(use_no_semi_2.o)
    0x080002b2   0x080002b2   0x00000002   PAD
    0x080002b4   0x080002b4   0x00000018   Code   RO         3591    .text               c_w.l(noretval__2printf.o)
    0x080002cc   0x080002cc   0x00000068   Code   RO         3593    .text               c_w.l(__printf.o)
    0x08000334   0x08000334   0x00000078   Code   RO         3595    .text               c_w.l(_printf_dec.o)
    0x080003ac   0x080003ac   0x00000044   Code   RO         3619    .text               c_w.l(rt_memclr.o)
    0x080003f0   0x080003f0   0x00000006   Code   RO         3621    .text               c_w.l(heapauxi.o)
    0x080003f6   0x080003f6   0x00000002   Code   RO         3703    .text               c_w.l(use_no_semi.o)
    0x080003f8   0x080003f8   0x00000016   Code   RO         3706    .text               c_w.l(_rserrno.o)
    0x0800040e   0x0800040e   0x000000b2   Code   RO         3708    .text               c_w.l(_printf_intcommon.o)
    0x080004c0   0x080004c0   0x00000024   Code   RO         3710    .text               c_w.l(_printf_char_file.o)
    0x080004e4   0x080004e4   0x0000004e   Code   RO         3713    .text               c_w.l(rt_memclr_w.o)
    0x08000532   0x08000532   0x00000002   PAD
    0x08000534   0x08000534   0x00000008   Code   RO         3788    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800053c   0x0800053c   0x00000030   Code   RO         3790    .text               c_w.l(_printf_char_common.o)
    0x0800056c   0x0800056c   0x00000008   Code   RO         3792    .text               c_w.l(ferror.o)
    0x08000574   0x08000574   0x00000008   Code   RO         3800    .text               c_w.l(libspace.o)
    0x0800057c   0x0800057c   0x0000004a   Code   RO         3803    .text               c_w.l(sys_stackheap_outer.o)
    0x080005c6   0x080005c6   0x00000012   Code   RO         3807    .text               c_w.l(exit.o)
    0x080005d8   0x080005d8   0x00000014   Code   RO         1950    i.ADC_Cmd           stm32f4xx_adc.o
    0x080005ec   0x080005ec   0x0000002c   Code   RO         1951    i.ADC_CommonInit    stm32f4xx_adc.o
    0x08000618   0x08000618   0x00000006   Code   RO         1962    i.ADC_GetConversionValue  stm32f4xx_adc.o
    0x0800061e   0x0800061e   0x0000000e   Code   RO         1963    i.ADC_GetFlagStatus  stm32f4xx_adc.o
    0x0800062c   0x0800062c   0x0000004c   Code   RO         1970    i.ADC_Init          stm32f4xx_adc.o
    0x08000678   0x08000678   0x00000074   Code   RO         1975    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x080006ec   0x080006ec   0x0000000a   Code   RO         1977    i.ADC_SoftwareStartConv  stm32f4xx_adc.o
    0x080006f6   0x080006f6   0x00000002   PAD
    0x080006f8   0x080006f8   0x00000138   Code   RO         3250    i.APP_Show          show.o
    0x08000830   0x08000830   0x00000080   Code   RO          353    i.Adc_Init          adc.o
    0x080008b0   0x080008b0   0x00000080   Code   RO          354    i.Adc_POWER_Init    adc.o
    0x08000930   0x08000930   0x00000002   Code   RO          219    i.BusFault_Handler  stm32f4xx_it.o
    0x08000932   0x08000932   0x00000002   PAD
    0x08000934   0x08000934   0x00000034   Code   RO          611    i.Buzzer_Init       led.o
    0x08000968   0x08000968   0x00000198   Code   RO          412    i.CAN1_Mode_Init    can.o
    0x08000b00   0x08000b00   0x000000f0   Code   RO          414    i.CAN1_RX0_IRQHandler  can.o
    0x08000bf0   0x08000bf0   0x00000094   Code   RO          416    i.CAN1_Rx_Msg       can.o
    0x08000c84   0x08000c84   0x00000036   Code   RO          419    i.CAN1_Send_Num     can.o
    0x08000cba   0x08000cba   0x00000002   PAD
    0x08000cbc   0x08000cbc   0x000000ac   Code   RO          420    i.CAN1_Tx_Msg       can.o
    0x08000d68   0x08000d68   0x00000064   Code   RO          421    i.CAN1_Tx_Staus     can.o
    0x08000dcc   0x08000dcc   0x00000010   Code   RO         2198    i.CAN_ITConfig      stm32f4xx_can.o
    0x08000ddc   0x08000ddc   0x00000058   Code   RO          963    i.CAN_SEND          usartx.o
    0x08000e34   0x08000e34   0x00000038   Code   RO          964    i.Check_Sum         usartx.o
    0x08000e6c   0x08000e6c   0x00000002   Code   RO          220    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000e6e   0x08000e6e   0x00000002   PAD
    0x08000e70   0x08000e70   0x000004b6   Code   RO         3018    i.Drive_Motor       balance.o
    0x08001326   0x08001326   0x00000002   PAD
    0x08001328   0x08001328   0x00000030   Code   RO          650    i.Enable_Pin        motor.o
    0x08001358   0x08001358   0x000000dc   Code   RO          486    i.Encoder_Init_TIM2  encoder.o
    0x08001434   0x08001434   0x000000bc   Code   RO          487    i.Encoder_Init_TIM3  encoder.o
    0x080014f0   0x080014f0   0x000000bc   Code   RO          488    i.Encoder_Init_TIM4  encoder.o
    0x080015ac   0x080015ac   0x000000bc   Code   RO          489    i.Encoder_Init_TIM5  encoder.o
    0x08001668   0x08001668   0x00000078   Code   RO         1269    i.GPIO_Init         stm32f4xx_gpio.o
    0x080016e0   0x080016e0   0x00000020   Code   RO         1270    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08001700   0x08001700   0x00000004   Code   RO         1277    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08001704   0x08001704   0x00000030   Code   RO          355    i.Get_Adc           adc.o
    0x08001734   0x08001734   0x00000030   Code   RO          356    i.Get_Adc2          adc.o
    0x08001764   0x08001764   0x000001c8   Code   RO         3019    i.Get_RC            balance.o
    0x0800192c   0x0800192c   0x00000110   Code   RO         3020    i.Get_Velocity_Form_Encoder  balance.o
    0x08001a3c   0x08001a3c   0x0000002e   Code   RO          357    i.Get_adc_Average   adc.o
    0x08001a6a   0x08001a6a   0x00000002   PAD
    0x08001a6c   0x08001a6c   0x00000068   Code   RO          358    i.Get_battery_volt  adc.o
    0x08001ad4   0x08001ad4   0x00000002   Code   RO          221    i.HardFault_Handler  stm32f4xx_it.o
    0x08001ad6   0x08001ad6   0x00000002   PAD
    0x08001ad8   0x08001ad8   0x00000040   Code   RO         3357    i.I2C_Ack           i2c.o
    0x08001b18   0x08001b18   0x00000044   Code   RO         3358    i.I2C_GPIOInit      i2c.o
    0x08001b5c   0x08001b5c   0x00000040   Code   RO         3359    i.I2C_NAck          i2c.o
    0x08001b9c   0x08001b9c   0x00000070   Code   RO         3361    i.I2C_ReadByte      i2c.o
    0x08001c0c   0x08001c0c   0x0000003e   Code   RO         3362    i.I2C_ReadOneByte   i2c.o
    0x08001c4a   0x08001c4a   0x00000002   PAD
    0x08001c4c   0x08001c4c   0x00000050   Code   RO         3363    i.I2C_Start         i2c.o
    0x08001c9c   0x08001c9c   0x00000040   Code   RO         3364    i.I2C_Stop          i2c.o
    0x08001cdc   0x08001cdc   0x00000054   Code   RO         3365    i.I2C_WaiteForAck   i2c.o
    0x08001d30   0x08001d30   0x0000004a   Code   RO         3366    i.I2C_WriteBits     i2c.o
    0x08001d7a   0x08001d7a   0x00000002   PAD
    0x08001d7c   0x08001d7c   0x00000060   Code   RO         3368    i.I2C_WriteByte     i2c.o
    0x08001ddc   0x08001ddc   0x00000032   Code   RO         3370    i.I2C_WriteOneByte  i2c.o
    0x08001e0e   0x08001e0e   0x00000002   PAD
    0x08001e10   0x08001e10   0x0000007c   Code   RO         3021    i.Incremental_PI_A  balance.o
    0x08001e8c   0x08001e8c   0x0000007c   Code   RO         3022    i.Incremental_PI_B  balance.o
    0x08001f08   0x08001f08   0x0000007c   Code   RO         3023    i.Incremental_PI_C  balance.o
    0x08001f84   0x08001f84   0x0000007c   Code   RO         3024    i.Incremental_PI_D  balance.o
    0x08002000   0x08002000   0x0000002c   Code   RO          561    i.KEY_Init          key.o
    0x0800202c   0x0800202c   0x00000024   Code   RO         3025    i.Key               balance.o
    0x08002050   0x08002050   0x00000040   Code   RO          612    i.LED_Init          led.o
    0x08002090   0x08002090   0x00000030   Code   RO          613    i.Led_Flash         led.o
    0x080020c0   0x080020c0   0x00000090   Code   RO         3026    i.Limit_Pwm         balance.o
    0x08002150   0x08002150   0x00000032   Code   RO         3459    i.MPU6050_Set_LPF   mpu6050.o
    0x08002182   0x08002182   0x00000030   Code   RO         3460    i.MPU6050_Set_Rate  mpu6050.o
    0x080021b2   0x080021b2   0x00000084   Code   RO         3462    i.MPU6050_initialize  mpu6050.o
    0x08002236   0x08002236   0x00000012   Code   RO         3465    i.MPU6050_setFullScaleAccelRange  mpu6050.o
    0x08002248   0x08002248   0x00000012   Code   RO         3466    i.MPU6050_setFullScaleGyroRange  mpu6050.o
    0x0800225a   0x0800225a   0x0000000e   Code   RO         3470    i.MPU6050_task      mpu6050.o
    0x08002268   0x08002268   0x00000054   Code   RO         3472    i.MPU_Get_Accelscope  mpu6050.o
    0x080022bc   0x080022bc   0x00000054   Code   RO         3473    i.MPU_Get_Gyroscope  mpu6050.o
    0x08002310   0x08002310   0x00000002   Code   RO          222    i.MemManage_Handler  stm32f4xx_it.o
    0x08002312   0x08002312   0x00000002   Code   RO          223    i.NMI_Handler       stm32f4xx_it.o
    0x08002314   0x08002314   0x00000064   Code   RO         1224    i.NVIC_Init         misc.o
    0x08002378   0x08002378   0x00000014   Code   RO         1225    i.NVIC_PriorityGroupConfig  misc.o
    0x0800238c   0x0800238c   0x0000002c   Code   RO          694    i.OLED_Clear        oled.o
    0x080023b8   0x080023b8   0x0000003c   Code   RO          697    i.OLED_DrawPoint    oled.o
    0x080023f4   0x080023f4   0x00000130   Code   RO          698    i.OLED_Init         oled.o
    0x08002524   0x08002524   0x00000048   Code   RO          699    i.OLED_Refresh_Gram  oled.o
    0x0800256c   0x0800256c   0x00000084   Code   RO          702    i.OLED_ShowChar     oled.o
    0x080025f0   0x080025f0   0x00000078   Code   RO          703    i.OLED_ShowNumber   oled.o
    0x08002668   0x08002668   0x0000003e   Code   RO          704    i.OLED_ShowString   oled.o
    0x080026a6   0x080026a6   0x00000002   PAD
    0x080026a8   0x080026a8   0x00000044   Code   RO          705    i.OLED_WR_Byte      oled.o
    0x080026ec   0x080026ec   0x00000018   Code   RO          792    i.PS2_ClearData     pstwo.o
    0x08002704   0x08002704   0x00000084   Code   RO          793    i.PS2_Cmd           pstwo.o
    0x08002788   0x08002788   0x00000040   Code   RO          794    i.PS2_DataKey       pstwo.o
    0x080027c8   0x080027c8   0x0000005c   Code   RO          795    i.PS2_EnterConfing  pstwo.o
    0x08002824   0x08002824   0x0000005c   Code   RO          796    i.PS2_ExitConfing   pstwo.o
    0x08002880   0x08002880   0x00000054   Code   RO          797    i.PS2_Init          pstwo.o
    0x080028d4   0x080028d4   0x000000d8   Code   RO          798    i.PS2_Read          pstwo.o
    0x080029ac   0x080029ac   0x000000a8   Code   RO          799    i.PS2_ReadData      pstwo.o
    0x08002a54   0x08002a54   0x0000001e   Code   RO          802    i.PS2_SetInit       pstwo.o
    0x08002a72   0x08002a72   0x00000002   PAD
    0x08002a74   0x08002a74   0x00000044   Code   RO          803    i.PS2_ShortPoll     pstwo.o
    0x08002ab8   0x08002ab8   0x00000058   Code   RO          804    i.PS2_TurnOnAnalogMode  pstwo.o
    0x08002b10   0x08002b10   0x0000019c   Code   RO         3027    i.PS2_control       balance.o
    0x08002cac   0x08002cac   0x0000000c   Code   RO         2896    i.PWR_BackupAccessCmd  stm32f4xx_pwr.o
    0x08002cb8   0x08002cb8   0x00000002   Code   RO          224    i.PendSV_Handler    stm32f4xx_it.o
    0x08002cba   0x08002cba   0x00000002   PAD
    0x08002cbc   0x08002cbc   0x00000018   Code   RO         1366    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08002cd4   0x08002cd4   0x00000018   Code   RO         1375    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08002cec   0x08002cec   0x00000018   Code   RO         1378    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08002d04   0x08002d04   0x00000018   Code   RO         1380    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x08002d1c   0x08002d1c   0x00000090   Code   RO         1387    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08002dac   0x08002dac   0x0000001c   Code   RO         1396    i.RCC_LSEConfig     stm32f4xx_rcc.o
    0x08002dc8   0x08002dc8   0x00000040   Code   RO          490    i.Read_Encoder      encoder.o
    0x08002e08   0x08002e08   0x000001c8   Code   RO         3028    i.Remote_Control    balance.o
    0x08002fd0   0x08002fd0   0x00000098   Code   RO         3221    i.Robot_Init        robot_select_init.o
    0x08003068   0x08003068   0x00000160   Code   RO         3222    i.Robot_Select      robot_select_init.o
    0x080031c8   0x080031c8   0x00000002   Code   RO          225    i.SVC_Handler       stm32f4xx_it.o
    0x080031ca   0x080031ca   0x00000002   PAD
    0x080031cc   0x080031cc   0x000000b4   Code   RO          316    i.SetSysClock       system_stm32f4xx.o
    0x08003280   0x08003280   0x00000074   Code   RO         3029    i.Set_Pwm           balance.o
    0x080032f4   0x080032f4   0x00000140   Code   RO         3030    i.Smooth_control    balance.o
    0x08003434   0x08003434   0x00000018   Code   RO         1228    i.SysTick_CLKSourceConfig  misc.o
    0x0800344c   0x0800344c   0x00000002   Code   RO          226    i.SysTick_Handler   stm32f4xx_it.o
    0x0800344e   0x0800344e   0x00000002   PAD
    0x08003450   0x08003450   0x0000005c   Code   RO          318    i.SystemInit        system_stm32f4xx.o
    0x080034ac   0x080034ac   0x0000002e   Code   RO         2349    i.TI1_Config        stm32f4xx_tim.o
    0x080034da   0x080034da   0x00000036   Code   RO         2350    i.TI2_Config        stm32f4xx_tim.o
    0x08003510   0x08003510   0x000000a0   Code   RO          651    i.TIM10_PWM_Init    motor.o
    0x080035b0   0x080035b0   0x000000a8   Code   RO          652    i.TIM11_PWM_Init    motor.o
    0x08003658   0x08003658   0x000000d0   Code   RO          907    i.TIM12_SERVO_Init  timer.o
    0x08003728   0x08003728   0x000000f8   Code   RO          653    i.TIM1_PWM_Init     motor.o
    0x08003820   0x08003820   0x00000010   Code   RO          491    i.TIM2_IRQHandler   encoder.o
    0x08003830   0x08003830   0x00000014   Code   RO          492    i.TIM3_IRQHandler   encoder.o
    0x08003844   0x08003844   0x00000014   Code   RO          493    i.TIM4_IRQHandler   encoder.o
    0x08003858   0x08003858   0x00000014   Code   RO          494    i.TIM5_IRQHandler   encoder.o
    0x0800386c   0x0800386c   0x000001e8   Code   RO         3031    i.TIM7_IRQHandler   balance.o
    0x08003a54   0x08003a54   0x0000005c   Code   RO          908    i.TIM7_Int_Init     timer.o
    0x08003ab0   0x08003ab0   0x00000014   Code   RO          495    i.TIM8_BRK_TIM12_IRQHandler  encoder.o
    0x08003ac4   0x08003ac4   0x00000258   Code   RO          909    i.TIM8_CC_IRQHandler  timer.o
    0x08003d1c   0x08003d1c   0x0000013c   Code   RO          910    i.TIM8_Cap_Init     timer.o
    0x08003e58   0x08003e58   0x00000010   Code   RO          912    i.TIM8_UP_TIM13_IRQHandler  timer.o
    0x08003e68   0x08003e68   0x000000b8   Code   RO          654    i.TIM9_PWM_Init     motor.o
    0x08003f20   0x08003f20   0x00000014   Code   RO         2351    i.TIM_ARRPreloadConfig  stm32f4xx_tim.o
    0x08003f34   0x08003f34   0x00000006   Code   RO         2357    i.TIM_ClearFlag     stm32f4xx_tim.o
    0x08003f3a   0x08003f3a   0x00000006   Code   RO         2358    i.TIM_ClearITPendingBit  stm32f4xx_tim.o
    0x08003f40   0x08003f40   0x00000014   Code   RO         2363    i.TIM_Cmd           stm32f4xx_tim.o
    0x08003f54   0x08003f54   0x00000016   Code   RO         2365    i.TIM_CtrlPWMOutputs  stm32f4xx_tim.o
    0x08003f6a   0x08003f6a   0x00000032   Code   RO         2372    i.TIM_EncoderInterfaceConfig  stm32f4xx_tim.o
    0x08003f9c   0x08003f9c   0x00000004   Code   RO         2378    i.TIM_GetCapture1   stm32f4xx_tim.o
    0x08003fa0   0x08003fa0   0x00000004   Code   RO         2379    i.TIM_GetCapture2   stm32f4xx_tim.o
    0x08003fa4   0x08003fa4   0x00000004   Code   RO         2380    i.TIM_GetCapture3   stm32f4xx_tim.o
    0x08003fa8   0x08003fa8   0x00000004   Code   RO         2381    i.TIM_GetCapture4   stm32f4xx_tim.o
    0x08003fac   0x08003fac   0x00000018   Code   RO         2384    i.TIM_GetITStatus   stm32f4xx_tim.o
    0x08003fc4   0x08003fc4   0x000000ac   Code   RO         2386    i.TIM_ICInit        stm32f4xx_tim.o
    0x08004070   0x08004070   0x00000010   Code   RO         2387    i.TIM_ICStructInit  stm32f4xx_tim.o
    0x08004080   0x08004080   0x00000010   Code   RO         2388    i.TIM_ITConfig      stm32f4xx_tim.o
    0x08004090   0x08004090   0x00000060   Code   RO         2392    i.TIM_OC1Init       stm32f4xx_tim.o
    0x080040f0   0x080040f0   0x0000000c   Code   RO         2394    i.TIM_OC1PolarityConfig  stm32f4xx_tim.o
    0x080040fc   0x080040fc   0x0000000c   Code   RO         2395    i.TIM_OC1PreloadConfig  stm32f4xx_tim.o
    0x08004108   0x08004108   0x00000080   Code   RO         2397    i.TIM_OC2Init       stm32f4xx_tim.o
    0x08004188   0x08004188   0x00000014   Code   RO         2399    i.TIM_OC2PolarityConfig  stm32f4xx_tim.o
    0x0800419c   0x0800419c   0x00000014   Code   RO         2400    i.TIM_OC2PreloadConfig  stm32f4xx_tim.o
    0x080041b0   0x080041b0   0x0000007c   Code   RO         2402    i.TIM_OC3Init       stm32f4xx_tim.o
    0x0800422c   0x0800422c   0x00000014   Code   RO         2404    i.TIM_OC3PolarityConfig  stm32f4xx_tim.o
    0x08004240   0x08004240   0x0000000c   Code   RO         2405    i.TIM_OC3PreloadConfig  stm32f4xx_tim.o
    0x0800424c   0x0800424c   0x00000060   Code   RO         2407    i.TIM_OC4Init       stm32f4xx_tim.o
    0x080042ac   0x080042ac   0x00000014   Code   RO         2408    i.TIM_OC4PolarityConfig  stm32f4xx_tim.o
    0x080042c0   0x080042c0   0x00000014   Code   RO         2409    i.TIM_OC4PreloadConfig  stm32f4xx_tim.o
    0x080042d4   0x080042d4   0x00000004   Code   RO         2429    i.TIM_SetCounter    stm32f4xx_tim.o
    0x080042d8   0x080042d8   0x00000010   Code   RO         2430    i.TIM_SetIC1Prescaler  stm32f4xx_tim.o
    0x080042e8   0x080042e8   0x00000018   Code   RO         2431    i.TIM_SetIC2Prescaler  stm32f4xx_tim.o
    0x08004300   0x08004300   0x00000010   Code   RO         2432    i.TIM_SetIC3Prescaler  stm32f4xx_tim.o
    0x08004310   0x08004310   0x00000018   Code   RO         2433    i.TIM_SetIC4Prescaler  stm32f4xx_tim.o
    0x08004328   0x08004328   0x0000007c   Code   RO         2435    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x080043a4   0x080043a4   0x00000012   Code   RO         2436    i.TIM_TimeBaseStructInit  stm32f4xx_tim.o
    0x080043b6   0x080043b6   0x00000002   PAD
    0x080043b8   0x080043b8   0x00000050   Code   RO         3032    i.Turn_Off          balance.o
    0x08004408   0x08004408   0x000000ec   Code   RO          965    i.UART5_IRQHandler  usartx.o
    0x080044f4   0x080044f4   0x000000f0   Code   RO          966    i.USART1_IRQHandler  usartx.o
    0x080045e4   0x080045e4   0x0000001c   Code   RO          967    i.USART1_SEND       usartx.o
    0x08004600   0x08004600   0x00000224   Code   RO          968    i.USART2_IRQHandler  usartx.o
    0x08004824   0x08004824   0x000000e4   Code   RO          969    i.USART3_IRQHandler  usartx.o
    0x08004908   0x08004908   0x0000001c   Code   RO          970    i.USART3_SEND       usartx.o
    0x08004924   0x08004924   0x0000001c   Code   RO          971    i.USART5_SEND       usartx.o
    0x08004940   0x08004940   0x00000014   Code   RO         1760    i.USART_Cmd         stm32f4xx_usart.o
    0x08004954   0x08004954   0x0000003e   Code   RO         1764    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08004992   0x08004992   0x00000030   Code   RO         1766    i.USART_ITConfig    stm32f4xx_usart.o
    0x080049c2   0x080049c2   0x00000002   PAD
    0x080049c4   0x080049c4   0x000000ac   Code   RO         1767    i.USART_Init        stm32f4xx_usart.o
    0x08004a70   0x08004a70   0x00000008   Code   RO         1774    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08004a78   0x08004a78   0x00000002   Code   RO          227    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004a7a   0x08004a7a   0x00000002   PAD
    0x08004a7c   0x08004a7c   0x000000ac   Code   RO          972    i.Vz_to_Akm_Angle   usartx.o
    0x08004b28   0x08004b28   0x00000054   Code   RO          973    i.XYZ_Target_Speed_transition  usartx.o
    0x08004b7c   0x08004b7c   0x00000030   Code   RO         3754    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08004bac   0x08004bac   0x00000004   PAD
    0x08004bb0   0x08004bb0   0x000002d8   Code   RO         3651    i.__hardfp_atan     m_wm.l(atan.o)
    0x08004e88   0x08004e88   0x00000c50   Code   RO         3665    i.__hardfp_pow      m_wm.l(pow.o)
    0x08005ad8   0x08005ad8   0x0000007a   Code   RO         3679    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08005b52   0x08005b52   0x00000006   PAD
    0x08005b58   0x08005b58   0x00000080   Code   RO         3691    i.__hardfp_tan      m_wm.l(tan.o)
    0x08005bd8   0x08005bd8   0x00000438   Code   RO         3759    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08006010   0x08006010   0x000000f8   Code   RO         3756    i.__kernel_poly     m_wm.l(poly.o)
    0x08006108   0x08006108   0x00000350   Code   RO         3764    i.__kernel_tan      m_wm.l(tan_i.o)
    0x08006458   0x08006458   0x00000030   Code   RO         3734    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08006488   0x08006488   0x00000014   Code   RO         3735    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800649c   0x0800649c   0x00000014   Code   RO         3736    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080064b0   0x080064b0   0x00000020   Code   RO         3737    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080064d0   0x080064d0   0x00000020   Code   RO         3738    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080064f0   0x080064f0   0x00000020   Code   RO         3740    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08006510   0x08006510   0x00000002   Code   RO         1173    i._sys_exit         usart.o
    0x08006512   0x08006512   0x00000002   PAD
    0x08006514   0x08006514   0x0000008c   Code   RO          565    i.click_N_Double_MPU6050  key.o
    0x080065a0   0x080065a0   0x00000024   Code   RO          974    i.data_task         usartx.o
    0x080065c4   0x080065c4   0x0000029c   Code   RO          975    i.data_transition   usartx.o
    0x08006860   0x08006860   0x0000002c   Code   RO         1115    i.delay_init        delay.o
    0x0800688c   0x0800688c   0x00000034   Code   RO         1116    i.delay_ms          delay.o
    0x080068c0   0x080068c0   0x00000034   Code   RO         1117    i.delay_us          delay.o
    0x080068f4   0x080068f4   0x00000034   Code   RO         1118    i.delay_xms         delay.o
    0x08006928   0x08006928   0x00000018   Code   RO         3750    i.fabs              m_wm.l(fabs.o)
    0x08006940   0x08006940   0x00000010   Code   RO         3033    i.float_abs         balance.o
    0x08006950   0x08006950   0x00000018   Code   RO         1174    i.fputc             usart.o
    0x08006968   0x08006968   0x00000020   Code   RO            3    i.main              main.o
    0x08006988   0x08006988   0x00000010   Code   RO          706    i.oled_pow          oled.o
    0x08006998   0x08006998   0x00000884   Code   RO         3251    i.oled_show         show.o
    0x0800721c   0x0800721c   0x0000004c   Code   RO         3252    i.show_task         show.o
    0x08007268   0x08007268   0x0000006e   Code   RO         3681    i.sqrt              m_wm.l(sqrt.o)
    0x080072d6   0x080072d6   0x000000c2   Code   RO         3289    i.systemInit        system.o
    0x08007398   0x08007398   0x00000020   Code   RO         3036    i.target_limit_float  balance.o
    0x080073b8   0x080073b8   0x00000010   Code   RO         3037    i.target_limit_int  balance.o
    0x080073c8   0x080073c8   0x000000ac   Code   RO          976    i.uart1_init        usartx.o
    0x08007474   0x08007474   0x000000a8   Code   RO          977    i.uart2_init        usartx.o
    0x0800751c   0x0800751c   0x000000b0   Code   RO          978    i.uart3_init        usartx.o
    0x080075cc   0x080075cc   0x000000dc   Code   RO          979    i.uart5_init        usartx.o
    0x080076a8   0x080076a8   0x00000010   Code   RO          980    i.usart1_send       usartx.o
    0x080076b8   0x080076b8   0x00000014   Code   RO          982    i.usart3_send       usartx.o
    0x080076cc   0x080076cc   0x00000010   Code   RO          983    i.usart5_send       usartx.o
    0x080076dc   0x080076dc   0x00000018   Code   RO         3715    x$fpl$basic         fz_wm.l(basic.o)
    0x080076f4   0x080076f4   0x00000062   Code   RO         3625    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08007756   0x08007756   0x00000002   PAD
    0x08007758   0x08007758   0x00000150   Code   RO         3627    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x080078a8   0x080078a8   0x00000010   Code   RO         3796    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x080078b8   0x080078b8   0x00000018   Code   RO         3798    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x080078d0   0x080078d0   0x000002b0   Code   RO         3634    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08007b80   0x08007b80   0x0000005e   Code   RO         3637    x$fpl$dfix          fz_wm.l(dfix.o)
    0x08007bde   0x08007bde   0x0000002e   Code   RO         3642    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08007c0c   0x08007c0c   0x00000026   Code   RO         3641    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08007c32   0x08007c32   0x00000002   PAD
    0x08007c34   0x08007c34   0x00000078   Code   RO         3717    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08007cac   0x08007cac   0x00000154   Code   RO         3647    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08007e00   0x08007e00   0x0000009c   Code   RO         3719    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08007e9c   0x08007e9c   0x0000000c   Code   RO         3721    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08007ea8   0x08007ea8   0x0000006c   Code   RO         3723    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08007f14   0x08007f14   0x00000016   Code   RO         3628    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08007f2a   0x08007f2a   0x00000002   PAD
    0x08007f2c   0x08007f2c   0x00000198   Code   RO         3725    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x080080c4   0x080080c4   0x000001d4   Code   RO         3629    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08008298   0x08008298   0x00000056   Code   RO         3649    x$fpl$f2d           fz_wm.l(f2d.o)
    0x080082ee   0x080082ee   0x0000008c   Code   RO         3727    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800837a   0x0800837a   0x0000000a   Code   RO         3875    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08008384   0x08008384   0x0000000a   Code   RO         3729    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800838e   0x0800838e   0x00000064   Code   RO         3815    x$fpl$retnan        fz_wm.l(retnan.o)
    0x080083f2   0x080083f2   0x0000005c   Code   RO         3731    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x0800844e   0x0800844e   0x00000030   Code   RO         3864    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0800847e   0x0800847e   0x00000000   Code   RO         3733    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800847e   0x0800847e   0x000014a4   Data   RO          708    .constdata          oled.o
    0x08009922   0x08009922   0x00000006   PAD
    0x08009928   0x08009928   0x00000098   Data   RO         3654    .constdata          m_wm.l(atan.o)
    0x080099c0   0x080099c0   0x00000088   Data   RO         3668    .constdata          m_wm.l(pow.o)
    0x08009a48   0x08009a48   0x00000008   Data   RO         3758    .constdata          m_wm.l(qnan.o)
    0x08009a50   0x08009a50   0x000000c8   Data   RO         3761    .constdata          m_wm.l(rred.o)
    0x08009b18   0x08009b18   0x00000060   Data   RO         3765    .constdata          m_wm.l(tan_i.o)
    0x08009b78   0x08009b78   0x00000020   Data   RO         3939    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009b98, Size: 0x00000d00, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00000044])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW          360    .data               adc.o
    0x20000004   COMPRESSED   0x00000004   Data   RW          362    .data               adc.o
    0x20000008   COMPRESSED   0x00000014   Data   RW          566    .data               key.o
    0x2000001c   COMPRESSED   0x00000004   Data   RW          615    .data               led.o
    0x20000020   COMPRESSED   0x00000028   Data   RW          808    .data               pstwo.o
    0x20000048   COMPRESSED   0x00000044   Data   RW          913    .data               timer.o
    0x2000008c   COMPRESSED   0x00000010   Data   RW          985    .data               usartx.o
    0x2000009c   COMPRESSED   0x00000004   Data   RW         1119    .data               delay.o
    0x200000a0   COMPRESSED   0x00000004   Data   RW         1178    .data               usart.o
    0x200000a4   COMPRESSED   0x00000010   Data   RW         1419    .data               stm32f4xx_rcc.o
    0x200000b4   COMPRESSED   0x00000004   Data   RW         3039    .data               balance.o
    0x200000b8   COMPRESSED   0x0000003c   Data   RW         3040    .data               balance.o
    0x200000f4   COMPRESSED   0x0000000c   Data   RW         3254    .data               show.o
    0x20000100   COMPRESSED   0x00000001   Data   RW         3295    .data               system.o
    0x20000101   COMPRESSED   0x00000001   Data   RW         3296    .data               system.o
    0x20000102   COMPRESSED   0x00000002   PAD
    0x20000104   COMPRESSED   0x00000004   Data   RW         3297    .data               system.o
    0x20000108   COMPRESSED   0x00000004   Data   RW         3298    .data               system.o
    0x2000010c   COMPRESSED   0x00000004   Data   RW         3299    .data               system.o
    0x20000110   COMPRESSED   0x00000001   Data   RW         3300    .data               system.o
    0x20000111   COMPRESSED   0x00000001   Data   RW         3301    .data               system.o
    0x20000112   COMPRESSED   0x00000001   Data   RW         3302    .data               system.o
    0x20000113   COMPRESSED   0x00000001   Data   RW         3303    .data               system.o
    0x20000114   COMPRESSED   0x00000001   Data   RW         3304    .data               system.o
    0x20000115   COMPRESSED   0x00000003   PAD
    0x20000118   COMPRESSED   0x00000004   Data   RW         3305    .data               system.o
    0x2000011c   COMPRESSED   0x00000004   Data   RW         3309    .data               system.o
    0x20000120   COMPRESSED   0x00000004   Data   RW         3310    .data               system.o
    0x20000124   COMPRESSED   0x00000004   Data   RW         3312    .data               system.o
    0x20000128   COMPRESSED   0x00000004   Data   RW         3313    .data               system.o
    0x2000012c   COMPRESSED   0x00000004   Data   RW         3314    .data               system.o
    0x20000130   COMPRESSED   0x00000004   Data   RW         3315    .data               system.o
    0x20000134   COMPRESSED   0x00000004   Data   RW         3316    .data               system.o
    0x20000138   COMPRESSED   0x00000004   Data   RW         3317    .data               system.o
    0x2000013c   COMPRESSED   0x00000004   Data   RW         3318    .data               system.o
    0x20000140   COMPRESSED   0x00000004   Data   RW         3319    .data               system.o
    0x20000144   COMPRESSED   0x00000004   Data   RW         3320    .data               system.o
    0x20000148   COMPRESSED   0x00000004   Data   RW         3321    .data               system.o
    0x2000014c   COMPRESSED   0x00000001   Data   RW         3322    .data               system.o
    0x2000014d   COMPRESSED   0x00000001   Data   RW         3323    .data               system.o
    0x2000014e   COMPRESSED   0x00000001   Data   RW         3324    .data               system.o
    0x2000014f   COMPRESSED   0x00000001   Data   RW         3325    .data               system.o
    0x20000150   COMPRESSED   0x00000001   Data   RW         3326    .data               system.o
    0x20000151   COMPRESSED   0x00000001   Data   RW         3327    .data               system.o
    0x20000152   COMPRESSED   0x00000002   PAD
    0x20000154   COMPRESSED   0x00000004   Data   RW         3328    .data               system.o
    0x20000158   COMPRESSED   0x00000004   Data   RW         3329    .data               system.o
    0x2000015c   COMPRESSED   0x00000004   Data   RW         3330    .data               system.o
    0x20000160   COMPRESSED   0x00000004   Data   RW         3331    .data               system.o
    0x20000164   COMPRESSED   0x00000004   Data   RW         3332    .data               system.o
    0x20000168   COMPRESSED   0x0000000c   Data   RW         3484    .data               mpu6050.o
    0x20000174   COMPRESSED   0x00000006   Data   RW         3487    .data               mpu6050.o
    0x2000017a   COMPRESSED   0x00000006   Data   RW         3488    .data               mpu6050.o
    0x20000180        -       0x00000400   Zero   RW          707    .bss                oled.o
    0x20000580        -       0x00000009   Zero   RW          807    .bss                pstwo.o
    0x20000589   COMPRESSED   0x00000003   PAD
    0x2000058c        -       0x00000090   Zero   RW          984    .bss                usartx.o
    0x2000061c        -       0x00000010   Zero   RW         3038    .bss                balance.o
    0x2000062c        -       0x00000018   Zero   RW         3223    .bss                robot_select_init.o
    0x20000644        -       0x0000000c   Zero   RW         3290    .bss                system.o
    0x20000650        -       0x00000014   Zero   RW         3291    .bss                system.o
    0x20000664        -       0x00000014   Zero   RW         3292    .bss                system.o
    0x20000678        -       0x00000014   Zero   RW         3293    .bss                system.o
    0x2000068c        -       0x00000014   Zero   RW         3294    .bss                system.o
    0x200006a0        -       0x00000060   Zero   RW         3801    .bss                c_w.l(libspace.o)
    0x20000700        -       0x00000200   Zero   RW         1216    HEAP                startup_stm32f40_41xxx.o
    0x20000900        -       0x00000400   Zero   RW         1215    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       502         60          0          8          0       4073   adc.o
      4546        642          0         64         16      15053   balance.o
      1122         80          0          0          0       5487   can.o
       200         18          0          4          0       2603   delay.o
       944         70          0          0          0       6111   encoder.o
       818         74          0          0          0       7101   i2c.o
       184         12          0         20          0       3102   key.o
       164         18          0          4          0       1661   led.o
        32          6          0          0          0     305755   main.o
       144         14          0          0          0       2153   misc.o
       808         44          0          0          0       3771   motor.o
       448         12          0         24          0       6273   mpu6050.o
       878         42       5284          0       1024       7559   oled.o
      1058        116          0         40          9       7343   pstwo.o
       504        142          0          0         24       1949   robot_select_init.o
      2568        402          0         12          0       3576   show.o
        64         26        392          0       1536        852   startup_stm32f40_41xxx.o
       286         20          0          0          0       5706   stm32f4xx_adc.o
        16          0          0          0          0       1591   stm32f4xx_can.o
       156          0          0          0          0       2362   stm32f4xx_gpio.o
        18          0          0          0          0       4138   stm32f4xx_it.o
        12          6          0          0          0        542   stm32f4xx_pwr.o
       268         42          0         16          0       5952   stm32f4xx_rcc.o
      1274         60          0          0          0      24464   stm32f4xx_tim.o
       310          8          0          0          0       4506   stm32f4xx_usart.o
       194          0          0         97         92       3750   system.o
       272         36          0          0          0       1521   system_stm32f4xx.o
      1232         62          0         68          0       5397   timer.o
        26          6          0          4          0       2920   usart.o
      3228        444          0         16        144      16281   usartx.o

    ----------------------------------------------------------------------
     22314       <USER>       <GROUP>        384       2848     463552   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          6          7          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       728        106        152          0          0        228   atan.o
       184         44          0          0          0        744   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       232          0          0          0          0        296   sqrt.o
       128         20          0          0          0        144   tan.o
       848         84         96          0          0        196   tan_i.o

    ----------------------------------------------------------------------
     11212        <USER>        <GROUP>          0         96       7384   Library Totals
        22          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1034         46          0          0         96       1492   c_w.l
      3484        252          0          0          0       3344   fz_wm.l
      6672        692        592          0          0       2548   m_wm.l

    ----------------------------------------------------------------------
     11212        <USER>        <GROUP>          0         96       7384   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     33526       3452       6306        384       2944     447608   Grand Totals
     33526       3452       6306         68       2944     447608   ELF Image Totals (compressed)
     33526       3452       6306         68          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                39832 (  38.90kB)
    Total RW  Size (RW Data + ZI Data)              3328 (   3.25kB)
    Total ROM Size (Code + RO Data + RW Data)      39900 (  38.96kB)

==============================================================================

