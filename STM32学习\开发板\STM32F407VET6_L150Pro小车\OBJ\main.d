..\obj\main.o: main.c
..\obj\main.o: ..\BALANCE\system.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\CORE\core_cm4.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\main.o: ..\CORE\core_cmInstr.h
..\obj\main.o: ..\CORE\core_cmFunc.h
..\obj\main.o: ..\CORE\core_cm4_simd.h
..\obj\main.o: ..\USER\system_stm32f4xx.h
..\obj\main.o: ..\CORE\arm_math.h
..\obj\main.o: ..\CORE\core_cm4.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\string.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\math.h
..\obj\main.o: ..\USER\stm32f4xx_conf.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\main.o: ..\FWLIB\inc\misc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\main.o: ..\SYSTEM\sys\sys.h
..\obj\main.o: ..\SYSTEM\delay\delay.h
..\obj\main.o: ..\SYSTEM\usart\usart.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\main.o: ..\BALANCE\balance.h
..\obj\main.o: ..\BALANCE\system.h
..\obj\main.o: ..\HARDWARE\led.h
..\obj\main.o: ..\HARDWARE\oled.h
..\obj\main.o: ..\HARDWARE\usartx.h
..\obj\main.o: ..\HARDWARE\adc.h
..\obj\main.o: ..\HARDWARE\can.h
..\obj\main.o: ..\HARDWARE\motor.h
..\obj\main.o: ..\HARDWARE\timer.h
..\obj\main.o: ..\HARDWARE\encoder.h
..\obj\main.o: ..\BALANCE\show.h
..\obj\main.o: ..\HARDWARE\pstwo.h
..\obj\main.o: ..\HARDWARE\key.h
..\obj\main.o: ..\BALANCE\robot_select_init.h
..\obj\main.o: ..\HARDWARE\MPU6050\I2C.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\main.o: ..\HARDWARE\MPU6050\MPU6050.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\main.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdarg.h
