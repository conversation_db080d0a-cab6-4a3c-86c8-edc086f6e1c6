..\obj\system.o: ..\BALANCE\system.c
..\obj\system.o: ..\BALANCE\system.h
..\obj\system.o: ..\USER\stm32f4xx.h
..\obj\system.o: ..\CORE\core_cm4.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\system.o: ..\CORE\core_cmInstr.h
..\obj\system.o: ..\CORE\core_cmFunc.h
..\obj\system.o: ..\CORE\core_cm4_simd.h
..\obj\system.o: ..\USER\system_stm32f4xx.h
..\obj\system.o: ..\CORE\arm_math.h
..\obj\system.o: ..\CORE\core_cm4.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\string.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\math.h
..\obj\system.o: ..\USER\stm32f4xx_conf.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\system.o: ..\USER\stm32f4xx.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\system.o: ..\FWLIB\inc\misc.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\system.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\system.o: ..\SYSTEM\sys\sys.h
..\obj\system.o: ..\SYSTEM\delay\delay.h
..\obj\system.o: ..\SYSTEM\usart\usart.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\system.o: ..\BALANCE\balance.h
..\obj\system.o: ..\BALANCE\system.h
..\obj\system.o: ..\HARDWARE\led.h
..\obj\system.o: ..\HARDWARE\oled.h
..\obj\system.o: ..\HARDWARE\usartx.h
..\obj\system.o: ..\HARDWARE\adc.h
..\obj\system.o: ..\HARDWARE\can.h
..\obj\system.o: ..\HARDWARE\motor.h
..\obj\system.o: ..\HARDWARE\timer.h
..\obj\system.o: ..\HARDWARE\encoder.h
..\obj\system.o: ..\BALANCE\show.h
..\obj\system.o: ..\HARDWARE\pstwo.h
..\obj\system.o: ..\HARDWARE\key.h
..\obj\system.o: ..\BALANCE\robot_select_init.h
..\obj\system.o: ..\HARDWARE\MPU6050\I2C.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\system.o: ..\HARDWARE\MPU6050\MPU6050.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\system.o: E:\DIAN\Keil5 MDK\ARM\ARMCC\Bin\..\include\stdarg.h
