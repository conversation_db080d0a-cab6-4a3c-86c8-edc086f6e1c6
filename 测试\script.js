class GomokuGame {
    constructor() {
        this.canvas = document.getElementById('game-board');
        this.ctx = this.canvas.getContext('2d');
        this.currentPlayerElement = document.getElementById('current-player');
        this.gameStatusElement = document.getElementById('game-status');
        this.restartBtn = document.getElementById('restart-btn');
        
        // 游戏配置
        this.boardSize = 15; // 15x15棋盘
        this.cellSize = 35;
        this.pieceRadius = 15;
        this.currentPlayer = 'black'; // 'black' 或 'white'
        this.gameOver = false;
        
        // 棋盘状态：0-空，1-黑子，2-白子
        this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(0));
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.drawBoard();
        this.bindEvents();
        this.updateUI();
    }
    
    setupCanvas() {
        const canvasSize = this.boardSize * this.cellSize + this.cellSize;
        this.canvas.width = canvasSize;
        this.canvas.height = canvasSize;
    }
    
    drawBoard() {
        const ctx = this.ctx;
        const canvasSize = this.canvas.width;
        
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);
        
        // 绘制背景
        ctx.fillStyle = '#DEB887';
        ctx.fillRect(0, 0, canvasSize, canvasSize);
        
        // 绘制网格线
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 1;
        
        const offset = this.cellSize / 2;
        
        // 绘制垂直线
        for (let i = 0; i < this.boardSize; i++) {
            const x = offset + i * this.cellSize;
            ctx.beginPath();
            ctx.moveTo(x, offset);
            ctx.lineTo(x, canvasSize - offset);
            ctx.stroke();
        }
        
        // 绘制水平线
        for (let i = 0; i < this.boardSize; i++) {
            const y = offset + i * this.cellSize;
            ctx.beginPath();
            ctx.moveTo(offset, y);
            ctx.lineTo(canvasSize - offset, y);
            ctx.stroke();
        }
        
        // 绘制天元和星位
        this.drawStarPoints();
        
        // 绘制已放置的棋子
        this.drawPieces();
    }
    
    drawStarPoints() {
        const ctx = this.ctx;
        const offset = this.cellSize / 2;
        const starPoints = [
            [3, 3], [3, 11], [7, 7], [11, 3], [11, 11]
        ];
        
        ctx.fillStyle = '#8B4513';
        starPoints.forEach(([x, y]) => {
            const pixelX = offset + x * this.cellSize;
            const pixelY = offset + y * this.cellSize;
            ctx.beginPath();
            ctx.arc(pixelX, pixelY, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    }
    
    drawPieces() {
        const ctx = this.ctx;
        const offset = this.cellSize / 2;
        
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] !== 0) {
                    const x = offset + col * this.cellSize;
                    const y = offset + row * this.cellSize;
                    
                    // 绘制棋子阴影
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                    ctx.beginPath();
                    ctx.arc(x + 2, y + 2, this.pieceRadius, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 绘制棋子
                    if (this.board[row][col] === 1) {
                        // 黑子
                        const gradient = ctx.createRadialGradient(x - 5, y - 5, 0, x, y, this.pieceRadius);
                        gradient.addColorStop(0, '#666');
                        gradient.addColorStop(1, '#000');
                        ctx.fillStyle = gradient;
                    } else {
                        // 白子
                        const gradient = ctx.createRadialGradient(x - 5, y - 5, 0, x, y, this.pieceRadius);
                        gradient.addColorStop(0, '#fff');
                        gradient.addColorStop(1, '#ddd');
                        ctx.fillStyle = gradient;
                    }
                    
                    ctx.beginPath();
                    ctx.arc(x, y, this.pieceRadius, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 棋子边框
                    ctx.strokeStyle = this.board[row][col] === 1 ? '#333' : '#999';
                    ctx.lineWidth = 1;
                    ctx.stroke();
                }
            }
        }
    }
    
    bindEvents() {
        this.canvas.addEventListener('click', (e) => {
            if (this.gameOver) return;
            
            const rect = this.canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const col = Math.round((x - this.cellSize / 2) / this.cellSize);
            const row = Math.round((y - this.cellSize / 2) / this.cellSize);
            
            if (this.isValidMove(row, col)) {
                this.makeMove(row, col);
            }
        });
        
        this.restartBtn.addEventListener('click', () => {
            this.restart();
        });
    }
    
    isValidMove(row, col) {
        return row >= 0 && row < this.boardSize && 
               col >= 0 && col < this.boardSize && 
               this.board[row][col] === 0;
    }
    
    makeMove(row, col) {
        const pieceValue = this.currentPlayer === 'black' ? 1 : 2;
        this.board[row][col] = pieceValue;
        
        this.drawBoard();
        
        if (this.checkWin(row, col, pieceValue)) {
            this.gameOver = true;
            this.gameStatusElement.textContent = `${this.currentPlayer === 'black' ? '黑子' : '白子'}获胜！`;
            this.gameStatusElement.style.color = '#e74c3c';
            this.canvas.classList.add('winner-animation');
            return;
        }
        
        if (this.isBoardFull()) {
            this.gameOver = true;
            this.gameStatusElement.textContent = '平局！';
            this.gameStatusElement.style.color = '#f39c12';
            return;
        }
        
        this.currentPlayer = this.currentPlayer === 'black' ? 'white' : 'black';
        this.updateUI();
    }
    
    checkWin(row, col, pieceValue) {
        const directions = [
            [0, 1],   // 水平
            [1, 0],   // 垂直
            [1, 1],   // 主对角线
            [1, -1]   // 副对角线
        ];
        
        for (let [dx, dy] of directions) {
            let count = 1; // 包含当前棋子
            
            // 向一个方向检查
            let r = row + dx;
            let c = col + dy;
            while (r >= 0 && r < this.boardSize && c >= 0 && c < this.boardSize && 
                   this.board[r][c] === pieceValue) {
                count++;
                r += dx;
                c += dy;
            }
            
            // 向相反方向检查
            r = row - dx;
            c = col - dy;
            while (r >= 0 && r < this.boardSize && c >= 0 && c < this.boardSize && 
                   this.board[r][c] === pieceValue) {
                count++;
                r -= dx;
                c -= dy;
            }
            
            if (count >= 5) {
                return true;
            }
        }
        
        return false;
    }
    
    isBoardFull() {
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] === 0) {
                    return false;
                }
            }
        }
        return true;
    }
    
    updateUI() {
        this.currentPlayerElement.textContent = this.currentPlayer === 'black' ? '黑子' : '白子';
        this.currentPlayerElement.style.color = this.currentPlayer === 'black' ? '#333' : '#666';
        
        if (!this.gameOver) {
            this.gameStatusElement.textContent = '游戏进行中';
            this.gameStatusElement.style.color = '#27ae60';
        }
    }
    
    restart() {
        this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(0));
        this.currentPlayer = 'black';
        this.gameOver = false;
        this.canvas.classList.remove('winner-animation');
        this.drawBoard();
        this.updateUI();
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    new GomokuGame();
});