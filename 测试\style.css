* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 800px;
    width: 100%;
}

h1 {
    color: #333;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.current-player {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
}

#current-player {
    color: #667eea;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.game-status {
    font-size: 1.1em;
    color: #555;
    font-weight: 500;
}

.restart-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.restart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.restart-btn:active {
    transform: translateY(0);
}

.board-container {
    display: flex;
    justify-content: center;
    margin: 20px 0;
    padding: 20px;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 15px;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
}

#game-board {
    border: 3px solid #8B4513;
    border-radius: 10px;
    cursor: crosshair;
    background: #DEB887;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

#game-board:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.instructions {
    text-align: left;
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.instructions ul {
    list-style-type: none;
    padding-left: 0;
}

.instructions li {
    margin: 8px 0;
    padding: 5px 0;
    color: #555;
    position: relative;
    padding-left: 20px;
}

.instructions li:before {
    content: "●";
    color: #667eea;
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        text-align: center;
    }
    
    #game-board {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    .instructions {
        text-align: center;
    }
}

/* 获胜动画 */
.winner-animation {
    animation: celebrate 1s ease-in-out;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}